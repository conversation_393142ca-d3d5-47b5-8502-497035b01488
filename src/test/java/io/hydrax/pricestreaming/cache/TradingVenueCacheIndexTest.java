package io.hydrax.pricestreaming.cache;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.hydrax.pricestreaming.utils.BeanUtil;
import io.hydrax.proto.metwo.match.ConfigAction;
import io.hydrax.proto.metwo.match.TickerRoute;
import io.hydrax.proto.metwo.match.VenueMarketUpdateRequest;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

/**
 * Test for TradingVenueCache index management
 */
public class TradingVenueCacheIndexTest {

  private TradingVenueCache tradingVenueCache;

  @BeforeEach
  void setUp() {
    tradingVenueCache = new TradingVenueCache();
  }

  @Test
  void testVenueUpdateClearsOldIndexes() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());

      // Create initial venue with BTCUSDT
      VenueMarketUpdateRequest venue1 = createTestVenue("testVenue", "BTCUSDT");
      tradingVenueCache.put(venue1);

      // Verify venue supports BTCUSDT
      List<String> result1 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      assertEquals(1, result1.size());
      assertEquals("testVenue", result1.get(0));

      // Verify venue doesn't support ETHUSDT
      List<String> result2 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "ETHUSDT");
      assertTrue(result2.isEmpty());

      // Update venue to support ETHUSDT instead of BTCUSDT
      VenueMarketUpdateRequest venue2 = createTestVenue("testVenue", "ETHUSDT");
      tradingVenueCache.put(venue2);

      // Now venue should NOT support BTCUSDT (old index should be cleared)
      List<String> result3 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      assertTrue(result3.isEmpty(), "Venue should no longer support BTCUSDT after update");

      // But should support ETHUSDT
      List<String> result4 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "ETHUSDT");
      assertEquals(1, result4.size());
      assertEquals("testVenue", result4.get(0));
    }
  }

  @Test
  void testVenueRemovalClearsIndexes() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());

      // Create venue
      VenueMarketUpdateRequest venue = createTestVenue("testVenue", "BTCUSDT");
      tradingVenueCache.put(venue);

      // Verify venue exists
      List<String> result1 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      assertEquals(1, result1.size());

      // Remove venue
      tradingVenueCache.remove(venue);

      // Verify venue is removed from indexes
      List<String> result2 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      assertTrue(result2.isEmpty(), "Venue should be removed from indexes after deletion");
    }
  }

  @Test
  void testOrderTypeChangeUpdatesIndexes() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());

      // Create venue with limit orders only
      VenueMarketUpdateRequest venue1 =
          VenueMarketUpdateRequest.newBuilder()
              .setAction(ConfigAction.UPDATE)
              .setCode("testVenue")
              .setDbId(1)
              .setName("testVenue")
              .setMarketCode("SPOT")
              .setPriceStreamingOrderTypes(
                  """
                  {"limit":{"enabled":true,"tifs":["gtc"]}}
                  """)
              .addTickersRoute(
                  TickerRoute.newBuilder()
                      .setLpTickerName("BTCUSDT_LP")
                      .setTickerCode("BTCUSDT")
                      .build())
              .build();
      tradingVenueCache.put(venue1);

      // Verify venue supports limit orders
      List<String> limitResult1 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      assertEquals(1, limitResult1.size());

      // Verify venue doesn't support market orders
      List<String> marketResult1 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "market", "BTCUSDT");
      assertTrue(marketResult1.isEmpty());

      // Update venue to support market orders only
      VenueMarketUpdateRequest venue2 =
          VenueMarketUpdateRequest.newBuilder()
              .setAction(ConfigAction.UPDATE)
              .setCode("testVenue")
              .setDbId(1)
              .setName("testVenue")
              .setMarketCode("SPOT")
              .setPriceStreamingOrderTypes(
                  """
                  {"market":{"enabled":true,"tifs":["gtc"]}}
                  """)
              .addTickersRoute(
                  TickerRoute.newBuilder()
                      .setLpTickerName("BTCUSDT_LP")
                      .setTickerCode("BTCUSDT")
                      .build())
              .build();
      tradingVenueCache.put(venue2);

      // Now venue should NOT support limit orders
      List<String> limitResult2 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      assertTrue(
          limitResult2.isEmpty(), "Venue should no longer support limit orders after update");

      // But should support market orders
      List<String> marketResult2 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "market", "BTCUSDT");
      assertEquals(1, marketResult2.size());
      assertEquals("testVenue", marketResult2.get(0));
    }
  }

  @Test
  void testCacheStatsAfterOperations() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());

      // Initial stats
      String initialStats = tradingVenueCache.getCacheStats();
      assertTrue(initialStats.contains("Hits: 0"));
      assertTrue(initialStats.contains("Misses: 0"));

      // Add venue and perform queries
      VenueMarketUpdateRequest venue = createTestVenue("testVenue", "BTCUSDT");
      tradingVenueCache.put(venue);

      // First query - cache miss
      tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");

      // Second query - cache hit
      tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");

      String finalStats = tradingVenueCache.getCacheStats();
      assertTrue(finalStats.contains("Hits: 1"));
      assertTrue(finalStats.contains("Misses: 1"));
      assertTrue(finalStats.contains("Hit Rate: 50.00%"));
    }
  }

  private VenueMarketUpdateRequest createTestVenue(String code, String ticker) {
    return VenueMarketUpdateRequest.newBuilder()
        .setAction(ConfigAction.UPDATE)
        .setCode(code)
        .setDbId(code.hashCode())
        .setName(code)
        .setMarketCode("SPOT")
        .setPriceStreamingOrderTypes(
            """
{"market":{"enabled":true,"tifs":["gtc"]},"limit":{"enabled":true,"tifs":["fok","gtc","ioc"]},"stop":{"enabled":false,"tifs":[]}}
""")
        .addTickersRoute(
            TickerRoute.newBuilder().setLpTickerName(ticker + "_LP").setTickerCode(ticker).build())
        .build();
  }
}
