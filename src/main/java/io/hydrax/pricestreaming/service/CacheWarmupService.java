package io.hydrax.pricestreaming.service;

import io.hydrax.pricestreaming.cache.TradingVenueCache;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import lombok.extern.slf4j.Slf4j;

/**
 * Service for asynchronous cache warmup and precomputation
 */
@Singleton
@Slf4j
public class CacheWarmupService {

  private final TradingVenueCache tradingVenueCache;
  private final Executor warmupExecutor;

  @Inject
  public CacheWarmupService(TradingVenueCache tradingVenueCache) {
    this.tradingVenueCache = tradingVenueCache;
    this.warmupExecutor =
        Executors.newSingleThreadExecutor(
            r -> {
              Thread t = new Thread(r, "cache-warmup");
              t.setDaemon(true);
              return t;
            });
  }

  /**
   * Asynchronously precompute common queries
   */
  public CompletableFuture<Void> warmupCacheAsync() {
    return CompletableFuture.runAsync(
        () -> {
          try {
            log.info("Starting cache warmup...");
            long startTime = System.currentTimeMillis();

            tradingVenueCache.precomputeCommonQueries();

            long duration = System.currentTimeMillis() - startTime;
            log.info(
                "Cache warmup completed in {}ms. {}", duration, tradingVenueCache.getCacheStats());
          } catch (Exception e) {
            log.error("Error during cache warmup", e);
          }
        },
        warmupExecutor);
  }

  /**
   * Warmup cache for specific trading pairs
   */
  public CompletableFuture<Void> warmupForTradingPairs(String[] tickers) {
    return CompletableFuture.runAsync(
        () -> {
          try {
            log.info("Starting targeted cache warmup for {} tickers", tickers.length);
            long startTime = System.currentTimeMillis();

            String[] orderTypes = {"limit", "market"};
            String[] timeInForces = {"gtc", "ioc", "fok"};

            for (String ticker : tickers) {
              for (String orderType : orderTypes) {
                for (String timeInForce : timeInForces) {
                  tradingVenueCache.selectCodeByTimeInForceAndOrderType(
                      timeInForce, orderType, ticker);
                }
              }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info(
                "Targeted cache warmup completed in {}ms for {} tickers. {}",
                duration,
                tickers.length,
                tradingVenueCache.getCacheStats());
          } catch (Exception e) {
            log.error("Error during targeted cache warmup", e);
          }
        },
        warmupExecutor);
  }

  /**
   * Get current cache statistics
   */
  public String getCacheStatistics() {
    return tradingVenueCache.getCacheStats();
  }
}
