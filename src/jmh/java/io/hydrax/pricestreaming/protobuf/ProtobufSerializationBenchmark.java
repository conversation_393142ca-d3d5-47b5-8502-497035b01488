package io.hydrax.pricestreaming.protobuf;

import com.google.protobuf.CodedInputStream;
import com.google.protobuf.CodedOutputStream;
import com.google.protobuf.InvalidProtocolBufferException;
import io.hydrax.proto.metwo.match.*;
import io.hydrax.pricestreaming.domain.ERResponseList;
import io.hydrax.pricestreaming.domain.PlaceOrder;
import io.hydrax.pricestreaming.utils.UDec128Util;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * Comprehensive JMH Benchmark for Protobuf Serialization Performance
 * 
 * This benchmark tests various protobuf serialization scenarios:
 * - Different message sizes (small, medium, large)
 * - Serialization vs deserialization performance
 * - Reused vs new builders
 * - Batch operations
 * - Memory allocation patterns
 * 
 * Run with:
 * ./gradlew jmh --include=".*ProtobufSerializationBenchmark.*"
 */
@BenchmarkMode({Mode.AverageTime, Mode.Throughput})
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(value = 1, jvmArgs = {"-Xms2G", "-Xmx2G", "-XX:+UseG1GC"})
@Warmup(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 3, timeUnit = TimeUnit.SECONDS)
public class ProtobufSerializationBenchmark {

    // Test data
    private PsOrder smallOrder;
    private PsOrder mediumOrder;
    private PsOrder largeOrder;
    private ResponseList smallResponseList;
    private ResponseList mediumResponseList;
    private ResponseList largeResponseList;
    private Request complexRequest;
    
    // Serialized data for deserialization tests
    private byte[] smallOrderBytes;
    private byte[] mediumOrderBytes;
    private byte[] largeOrderBytes;
    private byte[] smallResponseListBytes;
    private byte[] mediumResponseListBytes;
    private byte[] largeResponseListBytes;
    private byte[] complexRequestBytes;
    
    // Reusable builders for optimization tests
    private PsOrder.Builder reusableOrderBuilder;
    private Response.Builder reusableResponseBuilder;
    private ResponseList.Builder reusableResponseListBuilder;
    private Request.Builder reusableRequestBuilder;
    
    // Batch test data
    private List<PsOrder> orderBatch;
    private List<Response> responseBatch;
    
    @Setup
    public void setup() throws IOException {
        setupTestData();
        setupSerializedData();
        setupReusableBuilders();
        setupBatchData();
    }
    
    private void setupTestData() {
        // Small order - minimal fields
        smallOrder = PsOrder.newBuilder()
            .setOrderId("ORDER_001")
            .setSymbol("BTC/USD")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from("1.0"))
            .setPrice(UDec128Util.from("50000.0"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();
        
        // Medium order - more fields
        mediumOrder = PsOrder.newBuilder()
            .setOrderId("ORDER_002")
            .setSymbol("ETH/USD")
            .setSide(Side.SIDE_SELL)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from("10.5"))
            .setPrice(UDec128Util.from("3000.25"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL)
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setServiceAccountId("SERVICE_001")
            .setHoldingAccountId("HOLDING_001")
            .setMarketCode("CRYPTO")
            .setExpireTime(System.currentTimeMillis() + 3600000)
            .build();
        
        // Large order - all fields populated
        largeOrder = PsOrder.newBuilder()
            .setOrderId("ORDER_003_VERY_LONG_ORDER_ID_WITH_LOTS_OF_CHARACTERS")
            .setSymbol("VERY_LONG_SYMBOL_NAME_FOR_TESTING_PURPOSES")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_STOP)
            .setQty(UDec128Util.from("999999.123456789012345678"))
            .setPrice(UDec128Util.from("123456.789012345678901234"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_FILL_OR_KILL)
            .setRequestType(RequestType.REQUEST_TYPE_EDIT_ORDER)
            .setServiceAccountId("SERVICE_ACCOUNT_WITH_VERY_LONG_NAME_FOR_TESTING")
            .setHoldingAccountId("HOLDING_ACCOUNT_WITH_VERY_LONG_NAME_FOR_TESTING")
            .setMarketCode("CRYPTOCURRENCY_MARKET_WITH_LONG_NAME")
            .setExpireTime(System.currentTimeMillis() + ********)
            .setOrigClOrdId("ORIGINAL_CLIENT_ORDER_ID_WITH_LONG_NAME")
            .setClOrdId("CLIENT_ORDER_ID_WITH_VERY_LONG_NAME_FOR_TESTING")
            .build();
        
        setupResponseLists();
        setupComplexRequest();
    }
    
    private void setupResponseLists() {
        // Small response list
        smallResponseList = ResponseList.newBuilder()
            .setOutSequence(1L)
            .addResponses(Response.newBuilder()
                .setExecReport(ExecReport.newBuilder()
                    .setOrderId("ORDER_001")
                    .setExecType(ExecType.EXEC_TYPE_NEW)
                    .setOrdStatus(OrdStatus.ORD_STATUS_NEW)
                    .build())
                .build())
            .build();
        
        // Medium response list
        ResponseList.Builder mediumBuilder = ResponseList.newBuilder()
            .setOutSequence(2L);
        
        for (int i = 0; i < 5; i++) {
            mediumBuilder.addResponses(Response.newBuilder()
                .setExecReport(ExecReport.newBuilder()
                    .setOrderId("ORDER_" + String.format("%03d", i))
                    .setExecType(ExecType.EXEC_TYPE_TRADE)
                    .setOrdStatus(OrdStatus.ORD_STATUS_PARTIALLY_FILLED)
                    .setLastFillQty(UDec128Util.from("1.0"))
                    .setLastFillPrice(UDec128Util.from("50000.0"))
                    .setFillQty(UDec128Util.from(String.valueOf(i + 1)))
                    .setFillAvgPrice(UDec128Util.from("50000.0"))
                    .build())
                .build());
        }
        mediumResponseList = mediumBuilder.build();
        
        // Large response list
        ResponseList.Builder largeBuilder = ResponseList.newBuilder()
            .setOutSequence(3L);
        
        for (int i = 0; i < 50; i++) {
            largeBuilder.addResponses(Response.newBuilder()
                .setTrade(Trade.newBuilder()
                    .setTradeId("TRADE_" + String.format("%06d", i))
                    .setMakerOrderId("MAKER_ORDER_" + i)
                    .setTakerOrderId("TAKER_ORDER_" + i)
                    .setSymbol("BTC/USD")
                    .setQty(UDec128Util.from("0.1"))
                    .setPrice(UDec128Util.from("50000.0"))
                    .setTime(System.currentTimeMillis())
                    .build())
                .build());
        }
        largeResponseList = largeBuilder.build();
    }
    
    private void setupComplexRequest() {
        complexRequest = Request.newBuilder()
            .setPsOrder(largeOrder)
            .setSource("PRICE_STREAMING_ENGINE")
            .setRequestId(12345L)
            .build();
    }
    
    private void setupSerializedData() throws IOException {
        smallOrderBytes = smallOrder.toByteArray();
        mediumOrderBytes = mediumOrder.toByteArray();
        largeOrderBytes = largeOrder.toByteArray();
        smallResponseListBytes = smallResponseList.toByteArray();
        mediumResponseListBytes = mediumResponseList.toByteArray();
        largeResponseListBytes = largeResponseList.toByteArray();
        complexRequestBytes = complexRequest.toByteArray();
    }
    
    private void setupReusableBuilders() {
        reusableOrderBuilder = PsOrder.newBuilder();
        reusableResponseBuilder = Response.newBuilder();
        reusableResponseListBuilder = ResponseList.newBuilder();
        reusableRequestBuilder = Request.newBuilder();
    }
    
    private void setupBatchData() {
        orderBatch = new ArrayList<>();
        responseBatch = new ArrayList<>();
        
        for (int i = 0; i < 100; i++) {
            orderBatch.add(PsOrder.newBuilder()
                .setOrderId("BATCH_ORDER_" + i)
                .setSymbol("BTC/USD")
                .setSide(i % 2 == 0 ? Side.SIDE_BUY : Side.SIDE_SELL)
                .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
                .setQty(UDec128Util.from("1.0"))
                .setPrice(UDec128Util.from("50000.0"))
                .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
                .build());
            
            responseBatch.add(Response.newBuilder()
                .setExecReport(ExecReport.newBuilder()
                    .setOrderId("BATCH_ORDER_" + i)
                    .setExecType(ExecType.EXEC_TYPE_NEW)
                    .setOrdStatus(OrdStatus.ORD_STATUS_NEW)
                    .build())
                .build());
        }
    }
    
    // ========== Serialization Benchmarks ==========
    
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeSmallOrder(Blackhole bh) {
        byte[] result = smallOrder.toByteArray();
        bh.consume(result);
    }
    
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeMediumOrder(Blackhole bh) {
        byte[] result = mediumOrder.toByteArray();
        bh.consume(result);
    }
    
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeLargeOrder(Blackhole bh) {
        byte[] result = largeOrder.toByteArray();
        bh.consume(result);
    }
    
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeSmallResponseList(Blackhole bh) {
        byte[] result = smallResponseList.toByteArray();
        bh.consume(result);
    }
    
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeMediumResponseList(Blackhole bh) {
        byte[] result = mediumResponseList.toByteArray();
        bh.consume(result);
    }
    
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeLargeResponseList(Blackhole bh) {
        byte[] result = largeResponseList.toByteArray();
        bh.consume(result);
    }
    
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeComplexRequest(Blackhole bh) {
        byte[] result = complexRequest.toByteArray();
        bh.consume(result);
    }

    // ========== Deserialization Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void deserializeSmallOrder(Blackhole bh) throws InvalidProtocolBufferException {
        PsOrder result = PsOrder.parseFrom(smallOrderBytes);
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void deserializeMediumOrder(Blackhole bh) throws InvalidProtocolBufferException {
        PsOrder result = PsOrder.parseFrom(mediumOrderBytes);
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void deserializeLargeOrder(Blackhole bh) throws InvalidProtocolBufferException {
        PsOrder result = PsOrder.parseFrom(largeOrderBytes);
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void deserializeSmallResponseList(Blackhole bh) throws InvalidProtocolBufferException {
        ResponseList result = ResponseList.parseFrom(smallResponseListBytes);
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void deserializeMediumResponseList(Blackhole bh) throws InvalidProtocolBufferException {
        ResponseList result = ResponseList.parseFrom(mediumResponseListBytes);
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void deserializeLargeResponseList(Blackhole bh) throws InvalidProtocolBufferException {
        ResponseList result = ResponseList.parseFrom(largeResponseListBytes);
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void deserializeComplexRequest(Blackhole bh) throws InvalidProtocolBufferException {
        Request result = Request.parseFrom(complexRequestBytes);
        bh.consume(result);
    }

    // ========== Builder Reuse Optimization Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeWithNewBuilder(Blackhole bh) {
        PsOrder order = PsOrder.newBuilder()
            .setOrderId("NEW_BUILDER_ORDER")
            .setSymbol("BTC/USD")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from("1.0"))
            .setPrice(UDec128Util.from("50000.0"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();
        byte[] result = order.toByteArray();
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeWithReusedBuilder(Blackhole bh) {
        reusableOrderBuilder.clear();
        PsOrder order = reusableOrderBuilder
            .setOrderId("REUSED_BUILDER_ORDER")
            .setSymbol("BTC/USD")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from("1.0"))
            .setPrice(UDec128Util.from("50000.0"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();
        byte[] result = order.toByteArray();
        bh.consume(result);
    }

    // ========== CodedOutputStream Optimization Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeWithCodedOutputStream(Blackhole bh) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        CodedOutputStream cos = CodedOutputStream.newInstance(baos);
        smallOrder.writeTo(cos);
        cos.flush();
        byte[] result = baos.toByteArray();
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeWithPreallocatedStream(Blackhole bh) throws IOException {
        int size = smallOrder.getSerializedSize();
        ByteArrayOutputStream baos = new ByteArrayOutputStream(size);
        CodedOutputStream cos = CodedOutputStream.newInstance(baos);
        smallOrder.writeTo(cos);
        cos.flush();
        byte[] result = baos.toByteArray();
        bh.consume(result);
    }

    // ========== Batch Operation Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(100)
    public void serializeBatchOrders(Blackhole bh) {
        for (PsOrder order : orderBatch) {
            byte[] result = order.toByteArray();
            bh.consume(result);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(100)
    public void serializeBatchResponses(Blackhole bh) {
        for (Response response : responseBatch) {
            byte[] result = response.toByteArray();
            bh.consume(result);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void serializeBatchResponseList(Blackhole bh) {
        ResponseList.Builder builder = ResponseList.newBuilder()
            .setOutSequence(System.currentTimeMillis());

        for (Response response : responseBatch) {
            builder.addResponses(response);
        }

        ResponseList responseList = builder.build();
        byte[] result = responseList.toByteArray();
        bh.consume(result);
    }

    // ========== Memory Allocation Pattern Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void createOrderWithAllocation(Blackhole bh) {
        // Simulate creating order with new UDec128 instances
        UDec128 qty = UDec128.newBuilder()
            .setHigh(0L)
            .setLow(1000000000000000000L)
            .build();

        UDec128 price = UDec128.newBuilder()
            .setHigh(0L)
            .setLow(5000000000000000000L)
            .build();

        PsOrder order = PsOrder.newBuilder()
            .setOrderId("ALLOCATION_TEST")
            .setSymbol("BTC/USD")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(qty)
            .setPrice(price)
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();

        bh.consume(order);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void createOrderWithUtilityMethod(Blackhole bh) {
        // Use utility method for UDec128 creation
        PsOrder order = PsOrder.newBuilder()
            .setOrderId("UTILITY_TEST")
            .setSymbol("BTC/USD")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from("1.0"))
            .setPrice(UDec128Util.from("50000.0"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();

        bh.consume(order);
    }

    // ========== Real-world Scenario Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void fullSerializationCycle(Blackhole bh) throws InvalidProtocolBufferException {
        // Create order
        PsOrder order = PsOrder.newBuilder()
            .setOrderId("CYCLE_TEST_" + ThreadLocalRandom.current().nextLong())
            .setSymbol("BTC/USD")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from("1.0"))
            .setPrice(UDec128Util.from("50000.0"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();

        // Serialize
        byte[] serialized = order.toByteArray();

        // Deserialize
        PsOrder deserialized = PsOrder.parseFrom(serialized);

        bh.consume(deserialized);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void erResponseListCreation(Blackhole bh) {
        // Simulate creating ERResponseList as done in the application
        ERResponseList erResponseList = ERResponseList.builder()
            .responseList(ResponseList.newBuilder()
                .setOutSequence(System.currentTimeMillis())
                .addResponses(Response.newBuilder()
                    .setExecReport(ExecReport.newBuilder()
                        .setOrderId("ER_TEST")
                        .setExecType(ExecType.EXEC_TYPE_NEW)
                        .setOrdStatus(OrdStatus.ORD_STATUS_NEW)
                        .build())
                    .build())
                .build())
            .topic("ER")
            .build();

        byte[] result = erResponseList.toByteArray();
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void placeOrderCreation(Blackhole bh) {
        // Simulate creating PlaceOrder as done in the application
        Request request = Request.newBuilder()
            .setPsOrder(smallOrder)
            .setSource("BENCHMARK_TEST")
            .setOutSequence(System.currentTimeMillis())
            .build();

        PlaceOrder placeOrder = PlaceOrder.builder()
            .request(request)
            .topic("ORDER")
            .build();

        byte[] result = placeOrder.toByteArray();
        bh.consume(result);
    }
}
