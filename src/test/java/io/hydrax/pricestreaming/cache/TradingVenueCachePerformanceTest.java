package io.hydrax.pricestreaming.cache;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.hydrax.pricestreaming.utils.BeanUtil;
import io.hydrax.proto.metwo.match.ConfigAction;
import io.hydrax.proto.metwo.match.TickerRoute;
import io.hydrax.proto.metwo.match.VenueMarketUpdateRequest;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

/**
 * Performance test for TradingVenueCache optimizations
 */
public class TradingVenueCachePerformanceTest {

  private TradingVenueCache tradingVenueCache;

  @BeforeEach
  void setUp() {
    tradingVenueCache = new TradingVenueCache();
  }

  @Test
  void testSelectCodeByTimeInForceAndOrderTypePerformance() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());

      // Setup test data - multiple venues with different configurations
      setupTestVenues();

      // First call - this will populate the cache
      List<String> result1 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");

      // Measure performance - cache miss (fresh query)
      long startTime = System.nanoTime();
      List<String> result2 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "ETHUSDT");
      long cacheMissTime = System.nanoTime() - startTime;

      // Measure performance - cache hit (same query as result1)
      startTime = System.nanoTime();
      List<String> result3 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      long cacheHitTime = System.nanoTime() - startTime;

      // Verify results are consistent
      assertEquals(result1, result3);
      assertFalse(result1.isEmpty());
      assertFalse(result2.isEmpty());

      // Cache hit should be significantly faster (at least 5x faster)
      assertTrue(
          cacheHitTime < cacheMissTime / 5,
          String.format(
              "Cache hit (%d ns) should be much faster than cache miss (%d ns)",
              cacheHitTime, cacheMissTime));

      System.out.printf("Performance test results:%n");
      System.out.printf("Cache miss: %d ns%n", cacheMissTime);
      System.out.printf("Cache hit: %d ns%n", cacheHitTime);
      System.out.printf("Performance improvement: %.2fx%n", (double) cacheMissTime / cacheHitTime);
    }
  }

  @Test
  void testCacheInvalidationOnVenueUpdate() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());

      // Setup initial venue
      VenueMarketUpdateRequest venue1 = createTestVenue("venue1", "BTCUSDT");
      tradingVenueCache.put(venue1);

      // First query - should cache result
      List<String> result1 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      assertEquals(1, result1.size());
      assertEquals("venue1", result1.get(0));

      // Update venue with different ticker
      VenueMarketUpdateRequest venue1Updated = createTestVenue("venue1", "ETHUSDT");
      tradingVenueCache.put(venue1Updated);

      // Query should return empty result (cache should be invalidated)
      List<String> result2 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      assertTrue(result2.isEmpty());

      // Query with new ticker should work
      List<String> result3 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "ETHUSDT");
      assertEquals(1, result3.size());
      assertEquals("venue1", result3.get(0));
    }
  }

  @Test
  void testMultipleVenuesPerformance() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());

      // Setup many venues
      for (int i = 0; i < 100; i++) {
        VenueMarketUpdateRequest venue = createTestVenue("venue" + i, "BTCUSDT");
        tradingVenueCache.put(venue);
      }

      // Measure performance with many venues
      long startTime = System.nanoTime();
      List<String> result =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      long executionTime = System.nanoTime() - startTime;

      assertEquals(100, result.size());

      // Second call should be much faster (cached)
      startTime = System.nanoTime();
      List<String> result2 =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
      long cachedExecutionTime = System.nanoTime() - startTime;

      assertEquals(result, result2);
      assertTrue(cachedExecutionTime < executionTime / 5);

      System.out.printf("Multiple venues test:%n");
      System.out.printf("First call with 100 venues: %d ns%n", executionTime);
      System.out.printf("Cached call: %d ns%n", cachedExecutionTime);
      System.out.printf("Cache stats: %s%n", tradingVenueCache.getCacheStats());
    }
  }

  @Test
  void testVenueLookupMapPerformance() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());

      // Setup test venues
      setupTestVenues();

      // Test index rebuild
      long startTime = System.nanoTime();
      tradingVenueCache.rebuildIndexes();
      long rebuildTime = System.nanoTime() - startTime;

      // Test performance with direct venue lookup
      startTime = System.nanoTime();
      for (int i = 0; i < 1000; i++) {
        List<String> result =
            tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "BTCUSDT");
        assertFalse(result.isEmpty());
      }
      long batchTime = System.nanoTime() - startTime;

      System.out.printf("Index rebuild performance:%n");
      System.out.printf("Rebuild time: %d ns%n", rebuildTime);
      System.out.printf("1000 queries time: %d ns%n", batchTime);
      System.out.printf("Average per query: %.2f ns%n", batchTime / 1000.0);
      System.out.printf("Cache stats: %s%n", tradingVenueCache.getCacheStats());

      // Performance should be reasonable - allow more time for cache hits
      assertTrue(batchTime < 100_000_000, "1000 queries should complete in under 100ms");
    }
  }

  private void setupTestVenues() {
    // Create multiple test venues with different configurations
    String[] venues = {"binance", "coinbase", "kraken", "bybit", "okx"};
    String[] tickers = {"BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT"};

    for (String venue : venues) {
      for (String ticker : tickers) {
        VenueMarketUpdateRequest request = createTestVenue(venue + "_" + ticker, ticker);
        tradingVenueCache.put(request);
      }
    }
  }

  private VenueMarketUpdateRequest createTestVenue(String code, String ticker) {
    return VenueMarketUpdateRequest.newBuilder()
        .setAction(ConfigAction.UPDATE)
        .setCode(code)
        .setDbId(code.hashCode())
        .setName(code)
        .setMarketCode("SPOT")
        .setPriceStreamingOrderTypes(
            """
{"market":{"enabled":true,"tifs":["gtc"]},"limit":{"enabled":true,"tifs":["fok","gtc","ioc"]},"stop":{"enabled":false,"tifs":[]}}
""")
        .addTickersRoute(
            TickerRoute.newBuilder().setLpTickerName(ticker + "_LP").setTickerCode(ticker).build())
        .build();
  }
}
