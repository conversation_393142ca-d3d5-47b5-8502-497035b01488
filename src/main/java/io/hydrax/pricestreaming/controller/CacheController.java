package io.hydrax.pricestreaming.controller;

import io.hydrax.pricestreaming.cache.*;
import io.hydrax.pricestreaming.domain.TickerDTO;
import io.hydrax.pricestreaming.domain.TradingVenueAccountDTO;
import io.hydrax.pricestreaming.domain.TradingVenueDTO;
import io.hydrax.pricestreaming.router.Rule;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jboss.resteasy.reactive.RestForm;

@Slf4j
@Path("/cache")
@Singleton
@RequiredArgsConstructor
public class CacheController {

  final TradingVenueCache tradingVenueCache;
  final TradingVenueAccountCache tradingVenueAccountCache;
  final TickerCache tickerCache;
  final OrderRoutingStrategyCache orderRoutingStrategyCache;
  final OrderCache orderCache;

  @Inject
  @Named("serviceSequenceCache")
  SequenceCache serviceSequenceCache;

  @Inject
  @Named("clientSequenceCache")
  SequenceCache clientSequenceCache;

  @Path("/venue-markets")
  @GET
  public List<TradingVenueDTO> getVenueMarkets() {
    log.info("getVenueMarkets");
    return tradingVenueCache.getAll();
  }

  @Path("/venue-accounts")
  @GET
  public List<TradingVenueAccountDTO> getVenueAccounts() {
    log.info("getVenueAccounts");
    return tradingVenueAccountCache.getAll();
  }

  @Path("/tickers")
  @GET
  public List<TickerDTO> getTickers() {
    log.info("getTickers");
    return tickerCache.getAll();
  }

  @Path("/strategy")
  @GET
  public List<Rule> getStrategies() {
    log.info("getStrategies");
    return orderRoutingStrategyCache.getAll();
  }

  @Path("/open-orders/count")
  @GET
  public long countParentOrder() {
    log.info("getOpenOrders");
    return orderCache.countParentOrder();
  }

  @Path("/sequence")
  @GET
  public Map<String, Map<String, Long>> getSequence() {
    log.info("getSequence");
    return Map.of(
        "clientSequence", clientSequenceCache.getAll(),
        "serviceSequence", serviceSequenceCache.getAll());
  }

  @Path("/sequence/client")
  @PUT
  public void updateClientSequence(@RestForm String name, @RestForm long sequence) {
    log.info("updateClientSequence");
    clientSequenceCache.put(name, sequence);
  }

  @Path("/sequence/server")
  @PUT
  public void updateServerSequence(@RestForm String name, @RestForm long sequence) {
    log.info("updateServerSequence");
    serviceSequenceCache.put(name, sequence);
  }
}
