<!DOCTYPE html>
<html lang='en'>
<head>
<meta charset='utf-8'>
<style>
	body {margin: 0; padding: 10px; background-color: #ffffff}
	h1 {margin: 5px 0 0 0; font-size: 18px; font-weight: normal; text-align: center}
	header {margin: -24px 0 5px 0; line-height: 24px}
	button {font: 12px sans-serif; cursor: pointer}
	p {margin: 5px 0 5px 0}
	a {color: #0366d6}
	#hl {position: absolute; display: none; overflow: hidden; white-space: nowrap; pointer-events: none; background-color: #ffffe0; outline: 1px solid #ffc000; height: 15px}
	#hl span {padding: 0 3px 0 3px}
	#status {overflow: hidden; white-space: nowrap}
	#match {overflow: hidden; white-space: nowrap; display: none; float: right; text-align: right}
	#reset {cursor: pointer}
	#canvas {width: 100%; height: 1104px}
</style>
</head>
<body style='font: 12px Verdana, sans-serif'>
<h1>CPU profile</h1>
<header style='text-align: left'><button id='reverse' title='Reverse'>&#x1f53b;</button>&nbsp;&nbsp;<button id='search' title='Search'>&#x1f50d;</button></header>
<header style='text-align: right'>Produced by <a href='https://github.com/jvm-profiling-tools/async-profiler'>async-profiler</a></header>
<canvas id='canvas'></canvas>
<div id='hl'><span></span></div>
<p id='match'>Matched: <span id='matchval'></span> <span id='reset' title='Clear'>&#x274c;</span></p>
<p id='status'>&nbsp;</p>
<script>
	// Copyright 2020 Andrei Pangin
	// Licensed under the Apache License, Version 2.0.
	'use strict';
	var root, rootLevel, px, pattern;
	var reverse = false;
	const levels = Array(69);
	for (let h = 0; h < levels.length; h++) {
		levels[h] = [];
	}

	const canvas = document.getElementById('canvas');
	const c = canvas.getContext('2d');
	const hl = document.getElementById('hl');
	const status = document.getElementById('status');

	const canvasWidth = canvas.offsetWidth;
	const canvasHeight = canvas.offsetHeight;
	canvas.style.width = canvasWidth + 'px';
	canvas.width = canvasWidth * (devicePixelRatio || 1);
	canvas.height = canvasHeight * (devicePixelRatio || 1);
	if (devicePixelRatio) c.scale(devicePixelRatio, devicePixelRatio);
	c.font = document.body.style.font;

	const palette = [
		[0xb2e1b2, 20, 20, 20],
		[0x50e150, 30, 30, 30],
		[0x50cccc, 30, 30, 30],
		[0xe15a5a, 30, 40, 40],
		[0xc8c83c, 30, 30, 10],
		[0xe17d00, 30, 30,  0],
		[0xcce880, 20, 20, 20],
	];

	function getColor(p) {
		const v = Math.random();
		return '#' + (p[0] + ((p[1] * v) << 16 | (p[2] * v) << 8 | (p[3] * v))).toString(16);
	}

	function f(level, left, width, type, title, inln, c1, int) {
		levels[level].push({left: left, width: width, color: getColor(palette[type]), title: title,
			details: (int ? ', int=' + int : '') + (c1 ? ', c1=' + c1 : '') + (inln ? ', inln=' + inln : '')
		});
	}

	function samples(n) {
		return n === 1 ? '1 sample' : n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' samples';
	}

	function pct(a, b) {
		return a >= b ? '100' : (100 * a / b).toFixed(2);
	}

	function findFrame(frames, x) {
		let left = 0;
		let right = frames.length - 1;

		while (left <= right) {
			const mid = (left + right) >>> 1;
			const f = frames[mid];

			if (f.left > x) {
				right = mid - 1;
			} else if (f.left + f.width <= x) {
				left = mid + 1;
			} else {
				return f;
			}
		}

		if (frames[left] && (frames[left].left - x) * px < 0.5) return frames[left];
		if (frames[right] && (x - (frames[right].left + frames[right].width)) * px < 0.5) return frames[right];

		return null;
	}

	function search(r) {
		if (r === true && (r = prompt('Enter regexp to search:', '')) === null) {
			return;
		}

		pattern = r ? RegExp(r) : undefined;
		const matched = render(root, rootLevel);
		document.getElementById('matchval').textContent = pct(matched, root.width) + '%';
		document.getElementById('match').style.display = r ? 'inherit' : 'none';
	}

	function render(newRoot, newLevel) {
		if (root) {
			c.fillStyle = '#ffffff';
			c.fillRect(0, 0, canvasWidth, canvasHeight);
		}

		root = newRoot || levels[0][0];
		rootLevel = newLevel || 0;
		px = canvasWidth / root.width;

		const x0 = root.left;
		const x1 = x0 + root.width;
		const marked = [];

		function mark(f) {
			return marked[f.left] >= f.width || (marked[f.left] = f.width);
		}

		function totalMarked() {
			let total = 0;
			let left = 0;
			Object.keys(marked).sort(function(a, b) { return a - b; }).forEach(function(x) {
				if (+x >= left) {
					total += marked[x];
					left = +x + marked[x];
				}
			});
			return total;
		}

		function drawFrame(f, y, alpha) {
			if (f.left < x1 && f.left + f.width > x0) {
				c.fillStyle = pattern && f.title.match(pattern) && mark(f) ? '#ee00ee' : f.color;
				c.fillRect((f.left - x0) * px, y, f.width * px, 15);

				if (f.width * px >= 21) {
					const chars = Math.floor(f.width * px / 7);
					const title = f.title.length <= chars ? f.title : f.title.substring(0, chars - 2) + '..';
					c.fillStyle = '#000000';
					c.fillText(title, Math.max(f.left - x0, 0) * px + 3, y + 12, f.width * px - 6);
				}

				if (alpha) {
					c.fillStyle = 'rgba(255, 255, 255, 0.5)';
					c.fillRect((f.left - x0) * px, y, f.width * px, 15);
				}
			}
		}

		for (let h = 0; h < levels.length; h++) {
			const y = reverse ? h * 16 : canvasHeight - (h + 1) * 16;
			const frames = levels[h];
			for (let i = 0; i < frames.length; i++) {
				drawFrame(frames[i], y, h < rootLevel);
			}
		}

		return totalMarked();
	}

	canvas.onmousemove = function() {
		const h = Math.floor((reverse ? event.offsetY : (canvasHeight - event.offsetY)) / 16);
		if (h >= 0 && h < levels.length) {
			const f = findFrame(levels[h], event.offsetX / px + root.left);
			if (f) {
				if (f != root) getSelection().removeAllRanges();
				hl.style.left = (Math.max(f.left - root.left, 0) * px + canvas.offsetLeft) + 'px';
				hl.style.width = (Math.min(f.width, root.width) * px) + 'px';
				hl.style.top = ((reverse ? h * 16 : canvasHeight - (h + 1) * 16) + canvas.offsetTop) + 'px';
				hl.firstChild.textContent = f.title;
				hl.style.display = 'block';
				canvas.title = f.title + '\n(' + samples(f.width) + f.details + ', ' + pct(f.width, levels[0][0].width) + '%)';
				canvas.style.cursor = 'pointer';
				canvas.onclick = function() {
					if (f != root) {
						render(f, h);
						canvas.onmousemove();
					}
				};
				status.textContent = 'Function: ' + canvas.title;
				return;
			}
		}
		canvas.onmouseout();
	}

	canvas.onmouseout = function() {
		hl.style.display = 'none';
		status.textContent = '\xa0';
		canvas.title = '';
		canvas.style.cursor = '';
		canvas.onclick = '';
	}

	canvas.ondblclick = function() {
		getSelection().selectAllChildren(hl);
	}

	document.getElementById('reverse').onclick = function() {
		reverse = !reverse;
		render();
	}

	document.getElementById('search').onclick = function() {
		search(true);
	}

	document.getElementById('reset').onclick = function() {
		search(false);
	}

	window.onkeydown = function() {
		if (event.ctrlKey && event.keyCode === 70) {
			event.preventDefault();
			search(true);
		} else if (event.keyCode === 27) {
			search(false);
		}
	}

f(0,0,18429,3,'all')
f(1,0,1,3,'[GC_active]')
f(2,0,1,3,'[unknown]')
f(3,0,1,3,'__libc_disable_asynccancel')
f(1,1,1828,3,'[not_walkable_Java]')
f(2,1,3,1,'I2C/C2I adapters')
f(3,1,1,4,'CodeCache::find_blob(void*)')
f(3,2,2,4,'SharedRuntime::fixup_callers_callsite(Method*, unsigned char*)')
f(2,4,1823,3,'[unknown]')
f(3,4,1,4,'Klass::is_klass() const')
f(4,4,1,3,'[unknown]')
f(5,4,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(6,4,1,3,'__clock_gettime')
f(7,4,1,3,'[vdso]')
f(3,5,39,3,'[unknown]')
f(4,5,38,3,'__clock_gettime')
f(5,7,36,3,'[vdso]')
f(4,43,1,3,'clock_gettime')
f(3,44,2,3,'[vdso]')
f(3,46,1763,3,'__clock_gettime')
f(4,82,1727,3,'[vdso]')
f(3,1809,18,3,'clock_gettime')
f(2,1827,2,3,'_new_array_nozero_Java')
f(3,1827,2,4,'OptoRuntime::new_array_nozero_C(Klass*, int, JavaThread*)')
f(4,1827,1,4,'ImmutableOopMapSet::find_map_at_offset(int) const')
f(4,1828,1,4,'OptoRuntime::is_deoptimized_caller_frame(JavaThread*)')
f(5,1828,1,4,'CodeCache::find_blob(void*)')
f(1,1829,7,3,'[unknown]')
f(2,1829,1,4,'PSPushContentsClosure::do_oop(narrowOop*)')
f(3,1829,1,3,'[unknown]')
f(4,1829,1,3,'clock_gettime')
f(2,1830,2,3,'__clock_gettime')
f(3,1830,2,3,'[vdso]')
f(2,1832,1,3,'__lll_unlock_wake')
f(2,1833,2,3,'__pthread_disable_asynccancel')
f(2,1835,1,3,'do_futex_wait.constprop.1')
f(1,1836,83,3,'[unknown_Java]')
f(2,1836,1,1,'I2C/C2I adapters')
f(2,1837,2,3,'Interpreter')
f(2,1839,1,4,'SharedRuntime::fixup_callers_callsite(Method*, unsigned char*)')
f(2,1840,1,4,'SharedRuntime::notify_jvmti_vthread_end(oopDesc*, unsigned char, JavaThread*)')
f(2,1841,27,3,'[unknown]')
f(3,1841,4,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(3,1845,2,3,'[unknown]')
f(4,1845,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(4,1846,1,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(3,1847,3,3,'__clock_gettime')
f(4,1847,3,3,'[vdso]')
f(3,1850,3,4,'os::javaTimeMillis()')
f(3,1853,15,4,'os::javaTimeNanos()')
f(2,1868,2,3,'arrayof_jbyte_fill')
f(2,1870,1,1,'com/google/protobuf/LazyStringArrayList.size')
f(2,1871,1,1,'com/google/protobuf/UnknownFieldSet.getSerializedSize')
f(2,1872,1,1,'io/aeron/cluster/ConsensusAdapter.onFragment')
f(2,1873,1,1,'io/aeron/driver/Receiver.doWork')
f(2,1874,1,1,'io/hydrax/pricestreaming/common/OrderType$$Lambda.0x00007f9eb7c87cf8.test')
f(2,1875,1,1,'io/hydrax/pricestreaming/router/RoutingEngine$$Lambda.0x00007f9eb7c8f030.test')
f(2,1876,1,1,'io/hydrax/pricestreaming/router/SequenceStrategy_Bean.getScope')
f(2,1877,1,1,'io/hydrax/proto/metwo/match/PsOrder$Builder.setOrderId')
f(2,1878,1,1,'io/hydrax/proto/metwo/match/PsOrder.getFeeRate')
f(2,1879,1,1,'io/hydrax/proto/metwo/match/Response.getSerializedSize')
f(2,1880,1,1,'java/lang/String.<init>')
f(2,1881,1,1,'java/lang/String.equals')
f(2,1882,3,1,'java/lang/String.hashCode')
f(2,1885,1,1,'java/lang/VirtualThread.hashCode')
f(2,1886,1,1,'java/math/BigInteger.divideAndRemainder')
f(2,1887,1,1,'java/math/MutableBigInteger.divideOneWord')
f(2,1888,1,1,'java/util/ArrayList$ArrayListSpliterator.tryAdvance')
f(2,1889,1,1,'java/util/HashMap.afterNodeInsertion')
f(2,1890,1,1,'java/util/Spliterator.getExactSizeIfKnown')
f(2,1891,1,1,'java/util/stream/ReferencePipeline$2$1.begin')
f(2,1892,8,1,'java/util/stream/ReferencePipeline$3.opWrapSink')
f(2,1900,1,1,'org/agrona/BitUtil.align')
f(2,1901,1,4,'os::javaTimeMillis()')
f(2,1902,13,4,'os::javaTimeNanos()')
f(2,1915,1,1,'sun/nio/ch/DatagramDispatcher.write0')
f(2,1916,3,1,'sun/nio/ch/EPoll.wait')
f(1,1919,1,3,'__GI__IO_default_xsputn')
f(1,1920,3,3,'__GI_vfprintf')
f(2,1921,2,3,'__GI___printf_fp_l')
f(1,1923,1,3,'__malloc')
f(1,1924,3,3,'__new_sem_wait_slow.constprop.0')
f(1,1927,1,3,'_int_free')
f(1,1928,2,3,'_int_malloc')
f(1,1930,1,3,'_itoa_word')
f(1,1931,10727,1,'java/lang/Thread.run')
f(2,1931,10727,1,'java/lang/Thread.runWith')
f(3,1931,1201,1,'io/netty/util/concurrent/FastThreadLocalRunnable.run')
f(4,1931,1201,1,'io/netty/util/internal/ThreadExecutorMap$2.run')
f(5,1931,1201,1,'io/netty/util/concurrent/SingleThreadEventExecutor$4.run')
f(6,1931,1201,1,'io/netty/channel/nio/NioEventLoop.run')
f(7,1933,1,1,'io/netty/channel/DefaultSelectStrategy.calculateStrategy')
f(8,1933,1,1,'io/netty/channel/nio/NioEventLoop$1.get')
f(9,1933,1,1,'io/netty/channel/nio/NioEventLoop.selectNow')
f(10,1933,1,1,'io/netty/channel/nio/SelectedSelectionKeySetSelector.selectNow')
f(11,1933,1,1,'sun/nio/ch/SelectorImpl.selectNow')
f(12,1933,1,1,'sun/nio/ch/SelectorImpl.lockAndDoSelect')
f(13,1933,1,2,'sun/nio/ch/EPollSelectorImpl.doSelect',1,0,0)
f(14,1933,1,2,'sun/nio/ch/SelectorImpl.processDeregisterQueue',1,0,0)
f(7,1934,1,1,'io/netty/channel/SingleThreadEventLoop.hasTasks')
f(7,1935,8,1,'io/netty/channel/nio/NioEventLoop.select')
f(8,1935,8,1,'io/netty/channel/nio/SelectedSelectionKeySetSelector.select')
f(9,1935,8,1,'sun/nio/ch/SelectorImpl.select')
f(10,1935,8,1,'sun/nio/ch/SelectorImpl.lockAndDoSelect')
f(11,1935,8,2,'sun/nio/ch/EPollSelectorImpl.doSelect',3,0,0)
f(12,1935,4,1,'sun/nio/ch/EPoll.wait')
f(13,1935,4,3,'[unknown]')
f(14,1935,1,3,'__libc_disable_asynccancel')
f(14,1936,1,3,'__libc_enable_asynccancel')
f(14,1937,2,3,'epoll_wait')
f(12,1939,1,1,'sun/nio/ch/EPollSelectorImpl.processEvents')
f(13,1939,1,1,'sun/nio/ch/EPollSelectorImpl.clearInterrupt')
f(14,1939,1,2,'sun/nio/ch/EventFD.reset',1,0,0)
f(12,1940,1,2,'sun/nio/ch/EPollSelectorImpl.processUpdateQueue',1,0,0)
f(12,1941,1,2,'sun/nio/ch/SelectorImpl.end',1,0,0)
f(13,1941,1,2,'java/nio/channels/spi/AbstractSelector.end',1,0,0)
f(12,1942,1,2,'sun/nio/ch/SelectorImpl.processDeregisterQueue',1,0,0)
f(7,1943,1188,1,'io/netty/util/concurrent/SingleThreadEventExecutor.runAllTasks')
f(8,1944,1,2,'io/netty/channel/SingleThreadEventLoop.afterRunningAllTasks',1,0,0)
f(9,1944,1,2,'io/netty/util/concurrent/SingleThreadEventExecutor.runAllTasksFrom',1,0,0)
f(10,1944,1,2,'io/netty/util/concurrent/SingleThreadEventExecutor.pollTaskFrom',1,0,0)
f(8,1945,1180,1,'io/netty/util/concurrent/AbstractEventExecutor.safeExecute',1,0,0)
f(9,1945,1180,1,'io/netty/util/concurrent/AbstractEventExecutor.runTask',1,0,0)
f(10,1945,1135,1,'io/vertx/core/eventbus/impl/HandlerRegistration$$Lambda.0x00007f9eb79869c0.run')
f(11,1946,1134,1,'io/vertx/core/eventbus/impl/HandlerRegistration.lambda$receive$0',23,0,0)
f(12,1946,1134,1,'io/vertx/core/eventbus/impl/MessageConsumerImpl.doReceive',23,0,0)
f(13,1946,1134,1,'io/vertx/core/eventbus/impl/MessageConsumerImpl.deliver',23,0,0)
f(14,1946,1124,1,'io/vertx/core/eventbus/impl/HandlerRegistration.dispatch',13,0,0)
f(15,1946,1124,1,'io/vertx/core/eventbus/impl/DeliveryContextBase.dispatch',13,0,0)
f(16,1959,1110,1,'io/vertx/core/eventbus/impl/DeliveryContextBase.next')
f(17,1959,1108,1,'io/vertx/core/eventbus/impl/HandlerRegistration$InboundDeliveryContext.execute',3,0,0)
f(18,1959,1108,1,'io/vertx/core/eventbus/impl/MessageConsumerImpl.dispatch',3,0,0)
f(19,1959,1108,1,'io/vertx/core/impl/ContextInternal.dispatch',3,0,0)
f(20,1960,1,3,'__clock_gettime')
f(20,1961,1105,1,'io/quarkus/vertx/runtime/VertxEventBusConsumerRecorder$3$1.handle')
f(21,1962,1104,1,'io/quarkus/vertx/runtime/VertxEventBusConsumerRecorder$3$1.handle')
f(22,1962,13,1,'io/quarkus/vertx/core/runtime/context/VertxContextSafetyToggle.setContextSafe',3,0,0)
f(23,1962,3,2,'io/vertx/core/impl/ContextInternal.getLocal',3,0,0)
f(24,1962,3,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.getLocal',3,0,0)
f(25,1962,3,2,'io/vertx/core/impl/ContextInternal.localContextData',3,0,0)
f(26,1962,3,2,'io/vertx/core/spi/context/storage/ContextLocal.get',3,0,0)
f(27,1962,3,2,'io/vertx/core/spi/context/storage/ContextLocal.get',3,0,0)
f(28,1962,3,2,'io/vertx/core/impl/ContextBase.getLocal',3,0,0)
f(29,1962,3,2,'io/vertx/core/spi/context/storage/AccessMode$1.getOrCreate',3,0,0)
f(23,1965,10,1,'io/vertx/core/impl/ContextInternal.putLocal')
f(24,1965,10,1,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.putLocal')
f(25,1965,10,1,'java/util/concurrent/ConcurrentHashMap.put')
f(26,1965,10,1,'java/util/concurrent/ConcurrentHashMap.putVal')
f(27,1970,5,1,'java/util/concurrent/ConcurrentHashMap.initTable')
f(22,1975,3,2,'io/quarkus/vertx/core/runtime/context/VertxContextSafetyToggle.setCurrentContextSafe',3,0,0)
f(23,1975,3,2,'io/quarkus/vertx/core/runtime/context/VertxContextSafetyToggle.setContextSafe',3,0,0)
f(24,1975,2,2,'io/vertx/core/impl/ContextInternal.getLocal',2,0,0)
f(25,1975,2,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.getLocal',2,0,0)
f(26,1975,2,2,'io/vertx/core/impl/ContextInternal.localContextData',2,0,0)
f(27,1975,2,2,'io/vertx/core/spi/context/storage/ContextLocal.get',2,0,0)
f(28,1975,2,2,'io/vertx/core/spi/context/storage/ContextLocal.get',2,0,0)
f(29,1975,2,2,'io/vertx/core/impl/ContextBase.getLocal',2,0,0)
f(30,1975,2,2,'io/vertx/core/spi/context/storage/AccessMode$1.getOrCreate',2,0,0)
f(24,1977,1,2,'io/vertx/core/impl/ContextInternal.putLocal',1,0,0)
f(25,1977,1,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.putLocal',1,0,0)
f(26,1977,1,2,'io/vertx/core/impl/ContextInternal.localContextData',1,0,0)
f(27,1977,1,2,'io/vertx/core/spi/context/storage/ContextLocal.get',1,0,0)
f(28,1977,1,2,'io/vertx/core/spi/context/storage/ContextLocal.get',1,0,0)
f(29,1977,1,2,'io/vertx/core/impl/ContextBase.getLocal',1,0,0)
f(30,1977,1,2,'io/vertx/core/spi/context/storage/AccessMode$1.getOrCreate',1,0,0)
f(22,1978,1086,1,'io/quarkus/vertx/runtime/EventConsumerInvoker.invoke')
f(23,1978,5,1,'io/quarkus/arc/ManagedContext.activate')
f(24,1978,5,1,'io/quarkus/arc/impl/RequestContext.activate')
f(25,1978,5,2,'io/quarkus/vertx/runtime/VertxCurrentContextFactory$VertxCurrentContext.set',3,0,0)
f(26,1978,1,2,'io/quarkus/vertx/core/runtime/context/VertxContextSafetyToggle.setContextSafe',1,0,0)
f(27,1978,1,2,'io/vertx/core/impl/ContextInternal.getLocal',1,0,0)
f(28,1978,1,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.getLocal',1,0,0)
f(29,1978,1,2,'io/vertx/core/impl/ContextInternal.localContextData',1,0,0)
f(30,1978,1,2,'io/vertx/core/spi/context/storage/ContextLocal.get',1,0,0)
f(31,1978,1,2,'io/vertx/core/spi/context/storage/ContextLocal.get',1,0,0)
f(32,1978,1,2,'io/vertx/core/impl/ContextBase.getLocal',1,0,0)
f(33,1978,1,2,'io/vertx/core/spi/context/storage/AccessMode$1.getOrCreate',1,0,0)
f(26,1979,1,2,'io/vertx/core/impl/ContextInternal.getLocal',1,0,0)
f(27,1979,1,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.getLocal',1,0,0)
f(28,1979,1,2,'java/util/concurrent/ConcurrentHashMap.get',1,0,0)
f(26,1980,3,2,'io/vertx/core/impl/ContextInternal.putLocal',1,0,0)
f(27,1980,3,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.putLocal',1,0,0)
f(28,1980,1,2,'io/vertx/core/impl/ContextInternal.localContextData',1,0,0)
f(29,1980,1,2,'io/vertx/core/spi/context/storage/ContextLocal.get',1,0,0)
f(30,1980,1,2,'io/vertx/core/spi/context/storage/ContextLocal.get',1,0,0)
f(31,1980,1,2,'io/vertx/core/impl/ContextBase.getLocal',1,0,0)
f(32,1980,1,2,'io/vertx/core/spi/context/storage/AccessMode$1.getOrCreate',1,0,0)
f(28,1981,2,1,'java/util/concurrent/ConcurrentHashMap.put')
f(29,1981,2,1,'java/util/concurrent/ConcurrentHashMap.putVal')
f(23,1983,1,2,'io/quarkus/arc/impl/RequestContext.isActive',1,0,0)
f(24,1983,1,2,'io/quarkus/vertx/runtime/VertxCurrentContextFactory$VertxCurrentContext.get',1,0,0)
f(25,1983,1,2,'io/vertx/core/impl/ContextInternal.getLocal',1,0,0)
f(26,1983,1,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.getLocal',1,0,0)
f(27,1983,1,2,'io/vertx/core/impl/ContextInternal.localContextData',1,0,0)
f(28,1983,1,2,'io/vertx/core/spi/context/storage/ContextLocal.get',1,0,0)
f(29,1983,1,2,'io/vertx/core/spi/context/storage/ContextLocal.get',1,0,0)
f(30,1983,1,2,'io/vertx/core/impl/ContextBase.getLocal',1,0,0)
f(31,1983,1,2,'io/vertx/core/spi/context/storage/AccessMode$1.getOrCreate',1,0,0)
f(23,1984,1080,1,'io/quarkus/vertx/runtime/EventConsumerInvoker.invokeBean',2,0,0)
f(24,1986,1078,1,'io/hydrax/pricestreaming/events/ExecutionReportEvent_onExecReport_LazyInvoker_IB3KcwrlfSE_DBUzK6_1aVe-_nE.invoke')
f(25,1987,1077,1,'io/hydrax/pricestreaming/events/ExecutionReportEvent_onExecReport_Invoker_IB3KcwrlfSE_DBUzK6_1aVe-_nE.invoke',15,0,0)
f(26,1987,1077,1,'io/hydrax/pricestreaming/events/ExecutionReportEvent_ClientProxy.onExecReport',15,0,0)
f(27,1987,1061,1,'io/hydrax/pricestreaming/events/ExecutionReportEvent.onExecReport')
f(28,2002,402,1,'io/hydrax/aeron/client/ClientManager.send')
f(29,2003,401,1,'java/util/stream/ReferencePipeline.forEach',3,0,0)
f(30,2003,401,1,'java/util/stream/AbstractPipeline.evaluate',3,0,0)
f(31,2003,401,1,'java/util/stream/ForEachOps$ForEachOp$OfRef.evaluateSequential',3,0,0)
f(32,2003,401,1,'java/util/stream/ForEachOps$ForEachOp.evaluateSequential',3,0,0)
f(33,2003,401,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',3,0,0)
f(34,2003,396,1,'java/util/stream/AbstractPipeline.copyInto')
f(35,2003,381,1,'java/util/ArrayList$ArrayListSpliterator.forEachRemaining')
f(36,2003,2,3,'itable stub')
f(36,2005,379,1,'java/util/stream/ReferencePipeline$2$1.accept')
f(37,2006,1,1,'io/hydrax/aeron/client/MessageRouter$$Lambda.0x00007f9eb7c17040.test')
f(38,2006,1,2,'io/hydrax/aeron/client/MessageRouter.lambda$route$0',1,0,0)
f(39,2006,1,2,'io/hydrax/aeron/client/MessageRouter$MessageRouteRule.match',1,0,0)
f(40,2006,1,2,'java/lang/String.equals',1,0,0)
f(37,2007,1,3,'itable stub')
f(37,2008,376,1,'java/util/stream/ForEachOps$ForEachOp$OfRef.accept')
f(38,2008,376,1,'io/hydrax/aeron/client/ClientManager$$Lambda.0x00007f9eb7c17288.accept')
f(39,2008,376,1,'io/hydrax/aeron/client/ClientManager.lambda$send$5',1,0,0)
f(40,2008,376,1,'io/hydrax/aeron/client/ClientManager.processSend',1,0,0)
f(41,2008,376,1,'java/util/stream/ReferencePipeline$Head.forEach',1,0,0)
f(42,2009,375,1,'java/util/Spliterators$ArraySpliterator.forEachRemaining')
f(43,2009,375,1,'io/hydrax/aeron/client/ClientManager$$Lambda.0x00007f9eb7c174b0.accept')
f(44,2009,375,1,'io/hydrax/aeron/client/ClientManager.lambda$processSend$7',10,0,0)
f(45,2010,360,1,'io/hydrax/aeron/client/ArchiveClient.send')
f(46,2022,8,2,'io/aeron/Publication.offer',8,0,0)
f(47,2022,8,2,'io/aeron/ConcurrentPublication.offer',8,0,0)
f(46,2030,1,2,'io/hydrax/aeron/client/ArchiveClient.connectionAvailable',1,0,0)
f(46,2031,1,2,'io/hydrax/aeron/connection/ExponentialBackoffReconnectStrategy.isRunning',1,0,0)
f(47,2031,1,2,'io/hydrax/aeron/connection/ContinuousStrategy.isRunning',1,0,0)
f(46,2032,333,1,'io/hydrax/pricestreaming/domain/ERResponseList.toByteArray')
f(47,2032,333,1,'com/google/protobuf/AbstractMessageLite.toByteArray')
f(48,2032,123,1,'io/hydrax/proto/metwo/match/ResponseList.getSerializedSize')
f(49,2032,122,1,'com/google/protobuf/CodedOutputStream.computeMessageSize')
f(50,2032,122,1,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag')
f(51,2032,122,1,'io/hydrax/proto/metwo/match/Response.getSerializedSize')
f(52,2033,121,1,'com/google/protobuf/CodedOutputStream.computeMessageSize')
f(53,2033,121,1,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag',1,0,0)
f(54,2033,38,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.getSerializedSize')
f(55,2036,18,2,'com/google/protobuf/CodedOutputStream.computeMessageSize',14,0,0)
f(56,2036,18,2,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag',17,0,0)
f(57,2036,15,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',14,0,0)
f(57,2051,3,3,'itable stub')
f(55,2054,9,1,'com/google/protobuf/GeneratedMessageV3.computeStringSize')
f(56,2054,9,2,'com/google/protobuf/CodedOutputStream.computeStringSize',9,0,0)
f(57,2054,9,2,'com/google/protobuf/CodedOutputStream.computeStringSizeNoTag',9,0,0)
f(58,2054,9,2,'com/google/protobuf/Utf8.encodedLength',9,0,0)
f(59,2054,9,2,'java/lang/String.charAt',9,0,0)
f(55,2063,6,1,'com/google/protobuf/GeneratedMessageV3.isStringEmpty')
f(55,2069,1,1,'com/google/protobuf/UnknownFieldSet.getSerializedSize')
f(55,2070,1,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.getPremium')
f(54,2071,82,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.getSerializedSize')
f(55,2079,39,2,'com/google/protobuf/CodedOutputStream.computeMessageSize',15,0,0)
f(56,2079,36,2,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag',29,0,0)
f(57,2079,1,1,'io/hydrax/proto/metwo/match/FeeResponse.getSerializedSize')
f(57,2080,21,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',15,0,0)
f(58,2099,2,1,'com/google/protobuf/UnknownFieldSet.getSerializedSize')
f(57,2101,14,3,'itable stub')
f(56,2115,3,2,'com/google/protobuf/CodedOutputStream.computeTagSize',3,0,0)
f(57,2115,3,2,'com/google/protobuf/CodedOutputStream.computeUInt32SizeNoTag',3,0,0)
f(55,2118,27,1,'com/google/protobuf/GeneratedMessageV3.computeStringSize')
f(56,2119,26,2,'com/google/protobuf/CodedOutputStream.computeStringSize',26,0,0)
f(57,2119,26,2,'com/google/protobuf/CodedOutputStream.computeStringSizeNoTag',26,0,0)
f(58,2119,26,2,'com/google/protobuf/Utf8.encodedLength',26,0,0)
f(59,2119,26,2,'java/lang/String.charAt',26,0,0)
f(55,2145,5,1,'com/google/protobuf/GeneratedMessageV3.isStringEmpty')
f(55,2150,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.getTaxRate')
f(55,2151,2,1,'io/hydrax/proto/metwo/match/TimeInForce.getNumber')
f(54,2153,1,3,'itable stub')
f(49,2154,1,1,'java/util/Collections$UnmodifiableCollection.size')
f(50,2154,1,1,'java/util/ArrayList.size')
f(48,2155,210,1,'io/hydrax/proto/metwo/match/ResponseList.writeTo')
f(49,2155,208,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage',1,0,0)
f(50,2155,208,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',1,0,0)
f(51,2156,207,1,'io/hydrax/proto/metwo/match/Response.writeTo')
f(52,2157,204,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage')
f(53,2158,203,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',5,0,0)
f(54,2161,67,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.writeTo')
f(55,2170,16,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage',4,0,0)
f(56,2170,15,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',13,0,0)
f(57,2171,2,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',2,0,0)
f(57,2173,5,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',4,0,0)
f(57,2178,1,1,'io/hydrax/proto/metwo/match/UDec128.writeTo')
f(58,2178,1,2,'com/google/protobuf/UnknownFieldSet.writeTo',1,0,0)
f(59,2178,1,2,'java/util/TreeMap$EntrySet.iterator',1,0,0)
f(60,2178,1,2,'java/util/TreeMap.getFirstEntry',1,0,0)
f(57,2179,6,3,'itable stub')
f(56,2185,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeTag',1,0,0)
f(57,2185,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(55,2186,1,1,'com/google/protobuf/CodedOutputStream.writeEnum')
f(56,2186,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeInt32',1,0,0)
f(57,2186,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeTag',1,0,0)
f(58,2186,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(55,2187,3,1,'com/google/protobuf/GeneratedMessageV3.isStringEmpty')
f(55,2190,38,1,'com/google/protobuf/GeneratedMessageV3.writeString',4,0,0)
f(56,2191,37,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeString',10,0,0)
f(57,2191,36,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeStringNoTag',9,0,0)
f(58,2192,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(58,2193,28,1,'com/google/protobuf/Utf8.encode',7,0,0)
f(59,2200,21,1,'com/google/protobuf/Utf8$UnsafeProcessor.encodeUtf8')
f(60,2200,21,2,'java/lang/String.charAt',21,0,0)
f(58,2221,6,1,'com/google/protobuf/Utf8.encodedLength')
f(59,2221,6,2,'java/lang/String.charAt',6,0,0)
f(57,2227,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeTag',1,0,0)
f(58,2227,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(54,2228,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.getSerializedSize')
f(54,2229,130,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.writeTo')
f(55,2237,32,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage',4,0,0)
f(56,2238,31,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',14,0,0)
f(57,2240,3,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',2,0,0)
f(57,2243,18,1,'io/hydrax/proto/metwo/match/UDec128.writeTo',2,0,0)
f(58,2244,5,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64',5,0,0)
f(59,2244,5,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64NoTag',5,0,0)
f(58,2249,12,2,'com/google/protobuf/UnknownFieldSet.writeTo',12,0,0)
f(59,2249,12,2,'java/util/TreeMap$EntrySet.iterator',12,0,0)
f(60,2249,12,2,'java/util/TreeMap.getFirstEntry',12,0,0)
f(57,2261,8,3,'itable stub')
f(55,2269,1,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64')
f(56,2269,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeTag',1,0,0)
f(57,2269,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(55,2270,1,1,'com/google/protobuf/CodedOutputStream.writeEnum')
f(56,2270,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeInt32',1,0,0)
f(57,2270,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeTag',1,0,0)
f(58,2270,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(55,2271,84,1,'com/google/protobuf/GeneratedMessageV3.writeString',7,0,0)
f(56,2271,84,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeString',10,0,0)
f(57,2271,83,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeStringNoTag',9,0,0)
f(58,2271,3,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag')
f(58,2274,56,1,'com/google/protobuf/Utf8.encode',9,0,0)
f(59,2283,47,1,'com/google/protobuf/Utf8$UnsafeProcessor.encodeUtf8')
f(60,2284,46,2,'java/lang/String.charAt',46,0,0)
f(58,2330,24,1,'com/google/protobuf/Utf8.encodedLength')
f(59,2330,24,2,'java/lang/String.charAt',24,0,0)
f(57,2354,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeTag',1,0,0)
f(58,2354,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(55,2355,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.getEarmarkAmt')
f(55,2356,2,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.getQuantity')
f(55,2358,1,1,'io/hydrax/proto/metwo/match/Side.getNumber')
f(54,2359,2,3,'itable stub')
f(52,2361,2,2,'com/google/protobuf/UnknownFieldSet.writeTo',2,0,0)
f(53,2361,2,2,'java/util/TreeMap$EntrySet.iterator',2,0,0)
f(54,2361,2,2,'java/util/TreeMap.getFirstEntry',2,0,0)
f(49,2363,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64',1,0,0)
f(50,2363,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64NoTag',1,0,0)
f(49,2364,1,2,'com/google/protobuf/GeneratedMessageV3.writeString',1,0,0)
f(50,2364,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeString',1,0,0)
f(51,2364,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeStringNoTag',1,0,0)
f(52,2364,1,2,'com/google/protobuf/Utf8.encode',1,0,0)
f(46,2365,5,2,'io/netty/util/concurrent/FastThreadLocal.isSet',5,0,0)
f(47,2365,5,2,'io/netty/util/internal/InternalThreadLocalMap.getIfSet',5,0,0)
f(48,2367,1,3,'jbyte_disjoint_arraycopy_avx3')
f(48,2368,2,3,'jshort_disjoint_arraycopy_avx3')
f(45,2370,5,2,'io/hydrax/aeron/client/ClientManager.getClient',5,0,0)
f(46,2370,5,2,'java/util/HashMap.get',5,0,0)
f(47,2370,5,2,'java/util/HashMap.getNode',5,0,0)
f(45,2375,3,1,'io/hydrax/pricestreaming/aeron/config/AeronConfiguration$$Lambda.0x00007f9eb77836f0.get')
f(46,2375,3,1,'io/hydrax/pricestreaming/aeron/config/AeronConfiguration.lambda$sequenceProvider$1')
f(47,2375,3,1,'io/hydrax/pricestreaming/cache/SequenceCache.getAndIncrement')
f(48,2375,3,1,'java/util/concurrent/ConcurrentHashMap.computeIfAbsent')
f(45,2378,6,2,'java/util/HashMap.get',4,0,0)
f(46,2378,6,2,'java/util/HashMap.getNode',4,0,0)
f(47,2382,2,1,'java/lang/String.equals')
f(35,2384,7,1,'java/util/Spliterator.getExactSizeIfKnown')
f(36,2385,3,3,'itable stub')
f(36,2388,1,1,'java/util/ArrayList$ArrayListSpliterator.characteristics')
f(36,2389,2,1,'java/util/ArrayList$ArrayListSpliterator.estimateSize')
f(35,2391,2,1,'java/util/stream/ReferencePipeline$2$1.begin')
f(36,2391,2,1,'java/util/stream/Sink.begin')
f(35,2393,6,1,'java/util/stream/Sink$ChainedReference.end')
f(36,2394,2,3,'itable stub')
f(36,2396,3,1,'java/util/stream/Sink.end')
f(34,2399,5,2,'java/util/stream/AbstractPipeline.wrapSink',3,0,0)
f(35,2402,2,1,'java/util/stream/ReferencePipeline$2.opWrapSink')
f(36,2402,2,2,'java/util/stream/ReferencePipeline$2$1.<init>',2,0,0)
f(37,2402,2,2,'java/util/stream/Sink$ChainedReference.<init>',2,0,0)
f(28,2404,8,2,'io/hydrax/pricestreaming/cache/MarketCache.getMarketModelBySymbolCode',8,0,0)
f(29,2404,8,2,'java/util/concurrent/ConcurrentHashMap.get',8,0,0)
f(28,2412,1,1,'io/hydrax/pricestreaming/cache/OrderCache.getParentId')
f(29,2412,1,1,'it/unimi/dsi/fastutil/objects/Object2LongFunctions$SynchronizedFunction.get')
f(30,2412,1,1,'it/unimi/dsi/fastutil/objects/Object2LongFunctions$SynchronizedFunction.get')
f(31,2412,1,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(32,2412,1,4,'ObjectSynchronizer::enter(Handle, BasicLock*, JavaThread*)')
f(33,2412,1,4,'ObjectMonitor::enter(JavaThread*)')
f(34,2412,1,4,'ObjectMonitor::TrySpin(JavaThread*) [clone .part.0]')
f(28,2413,3,1,'io/hydrax/pricestreaming/cache/OrderCache.getParentOrder')
f(29,2413,3,1,'it/unimi/dsi/fastutil/longs/Long2ObjectFunctions$SynchronizedFunction.get')
f(30,2413,3,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(31,2413,3,4,'ObjectSynchronizer::enter(Handle, BasicLock*, JavaThread*)')
f(32,2413,3,4,'ObjectMonitor::enter(JavaThread*)')
f(33,2413,1,4,'ObjectMonitor::TrySpin(JavaThread*) [clone .part.0]')
f(33,2414,1,3,'SpinPause')
f(33,2415,1,3,'_SafeFetch32_fault')
f(28,2416,13,1,'io/hydrax/pricestreaming/cache/OrderCache.getParentOrderRemainingEarmark')
f(29,2416,13,2,'java/util/concurrent/ConcurrentHashMap.getOrDefault',13,0,0)
f(30,2416,13,2,'java/util/concurrent/ConcurrentHashMap.get',13,0,0)
f(28,2429,1,1,'io/hydrax/pricestreaming/cache/OrderCache.removeChild')
f(29,2429,1,1,'it/unimi/dsi/fastutil/objects/Object2ObjectFunctions$SynchronizedFunction.remove')
f(30,2429,1,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(31,2429,1,4,'ObjectSynchronizer::enter(Handle, BasicLock*, JavaThread*)')
f(32,2429,1,4,'ObjectMonitor::enter(JavaThread*)')
f(33,2429,1,4,'ObjectMonitor::TrySpin(JavaThread*) [clone .part.0]')
f(28,2430,1,2,'io/hydrax/pricestreaming/cache/OrderCache.removeParent',1,0,0)
f(29,2430,1,2,'it/unimi/dsi/fastutil/longs/Long2ObjectFunctions$SynchronizedFunction.remove',1,0,0)
f(30,2430,1,2,'it/unimi/dsi/fastutil/longs/Long2ObjectMap.remove',1,0,0)
f(31,2430,1,2,'it/unimi/dsi/fastutil/longs/Long2ObjectFunction.remove',1,0,0)
f(32,2430,1,2,'it/unimi/dsi/fastutil/longs/Long2ObjectOpenHashMap.containsKey',1,0,0)
f(28,2431,4,1,'io/hydrax/pricestreaming/config/MarketModelServiceFactory.get')
f(29,2431,4,2,'java/util/HashMap.get',2,0,0)
f(30,2431,4,2,'java/util/HashMap.getNode',2,0,0)
f(31,2433,2,1,'java/lang/String.equals')
f(28,2435,1,1,'io/hydrax/pricestreaming/domain/ERResponseList.setResponseList')
f(28,2436,5,2,'io/hydrax/pricestreaming/events/ExecutionReportEvent.getExecIncludePremiumAveragePrice',2,0,0)
f(29,2436,5,2,'io/hydrax/pricestreaming/utils/UDec128Util.from',2,0,0)
f(30,2436,5,2,'io/hydrax/pricestreaming/utils/UDec128Util.fromBigDecimalToUDec128',2,0,0)
f(31,2437,1,1,'io/hydrax/proto/metwo/match/UDec128$Builder.build')
f(32,2437,1,1,'io/hydrax/proto/metwo/match/UDec128$Builder.buildPartial')
f(33,2437,1,1,'io/hydrax/proto/metwo/match/UDec128.<init>')
f(34,2437,1,2,'com/google/protobuf/GeneratedMessageV3.<init>',1,0,0)
f(35,2437,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.getUnknownFields',1,0,0)
f(31,2438,1,1,'java/math/BigDecimal.multiply')
f(32,2438,1,2,'java/math/BigDecimal.multiply',1,0,0)
f(31,2439,1,1,'java/math/BigDecimal.toBigInteger')
f(32,2439,1,1,'java/math/BigDecimal.setScale')
f(31,2440,1,2,'java/math/BigInteger.shiftRight',1,0,0)
f(32,2440,1,2,'java/math/BigInteger.shiftRightImpl',1,0,0)
f(28,2441,13,1,'io/hydrax/pricestreaming/service/impl/BrokerService.getTradingVenueAccount')
f(29,2442,12,2,'io/hydrax/pricestreaming/cache/TradingVenueAccountCache.get',12,0,0)
f(30,2442,12,2,'java/util/concurrent/ConcurrentHashMap.get',12,0,0)
f(28,2454,8,1,'io/hydrax/pricestreaming/utils/IdUtil.formatId')
f(29,2456,2,1,'java/lang/Long.toString')
f(29,2458,2,1,'java/lang/StringBuilder.append')
f(30,2458,2,2,'java/lang/AbstractStringBuilder.append',2,0,0)
f(31,2458,2,2,'java/lang/Long.getChars',2,0,0)
f(29,2460,2,2,'java/lang/StringBuilder.toString',2,0,0)
f(30,2460,2,2,'java/lang/String.<init>',2,0,0)
f(31,2460,2,2,'java/lang/String.<init>',2,0,0)
f(32,2460,2,3,'jlong_disjoint_arraycopy_avx3')
f(28,2462,12,1,'io/hydrax/pricestreaming/utils/UDec128Util.from')
f(29,2462,12,2,'io/hydrax/pricestreaming/utils/UDec128Util.fromBigDecimalToUDec128',4,0,0)
f(30,2462,5,1,'java/math/BigDecimal.multiply')
f(31,2462,5,2,'java/math/BigDecimal.multiply',3,0,0)
f(32,2465,2,1,'java/math/BigDecimal.valueOf')
f(30,2467,3,1,'java/math/BigDecimal.toBigInteger')
f(31,2467,3,1,'java/math/BigDecimal.setScale')
f(32,2467,1,1,'java/math/BigDecimal.divideAndRound')
f(33,2467,1,2,'java/math/BigDecimal.valueOf',1,0,0)
f(32,2468,2,3,'jint_disjoint_arraycopy_avx3')
f(30,2470,4,2,'java/math/BigInteger.longValue',4,0,0)
f(31,2470,4,2,'java/math/BigInteger.getInt',4,0,0)
f(28,2474,406,1,'io/hydrax/pricestreaming/utils/UDec128Util.toBigDecimal')
f(29,2474,18,2,'java/lang/Long.toUnsignedString',6,0,0)
f(30,2474,18,2,'java/lang/Long.toUnsignedString',6,0,0)
f(31,2474,18,2,'java/lang/Long.toString',6,0,0)
f(32,2480,12,1,'java/lang/Long.toString')
f(29,2492,332,1,'java/math/BigDecimal.divide',14,0,0)
f(30,2492,322,1,'java/math/BigDecimal.divide',7,0,0)
f(31,2499,314,1,'java/math/BigDecimal.divideAndRound')
f(32,2499,290,1,'java/math/BigDecimal.createAndStripZerosToMatchScale',35,0,0)
f(33,2530,4,2,'java/math/BigDecimal.valueOf',3,0,0)
f(34,2533,1,1,'java/math/BigDecimal.compactValFor')
f(33,2534,254,1,'java/math/BigInteger.divideAndRemainder')
f(34,2536,252,2,'java/math/BigInteger.divideAndRemainderKnuth',103,0,0)
f(35,2585,1,4,'SafepointSynchronize::handle_polling_page_exception(JavaThread*)')
f(35,2586,1,4,'SharedRuntime::on_slowpath_allocation_exit(JavaThread*)')
f(35,2587,1,3,'arrayof_jint_fill')
f(35,2588,195,1,'java/math/MutableBigInteger.divideKnuth',49,0,0)
f(36,2588,195,1,'java/math/MutableBigInteger.divideKnuth',49,0,0)
f(37,2637,146,1,'java/math/MutableBigInteger.divideOneWord')
f(35,2783,2,1,'java/math/MutableBigInteger.toBigInteger')
f(36,2783,2,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(37,2783,1,4,'Klass::check_array_allocation_length(int, int, JavaThread*)')
f(37,2784,1,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(38,2784,1,4,'MemAllocator::allocate() const')
f(39,2784,1,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(35,2785,3,3,'jint_disjoint_arraycopy_avx3')
f(33,2788,1,3,'jint_disjoint_arraycopy_avx3')
f(32,2789,23,1,'java/math/MutableBigInteger.divide',4,0,0)
f(33,2793,19,1,'java/math/MutableBigInteger.divideLongMagnitude')
f(34,2805,7,2,'java/math/MutableBigInteger.divWord',7,0,0)
f(32,2812,1,1,'java/math/MutableBigInteger.toBigDecimal')
f(33,2812,1,1,'java/math/BigDecimal.valueOf')
f(34,2812,1,4,'OptoRuntime::new_array_nozero_C(Klass*, int, JavaThread*)')
f(35,2812,1,4,'OptoRuntime::is_deoptimized_caller_frame(JavaThread*)')
f(36,2812,1,4,'CodeCache::find_blob(void*)')
f(31,2813,1,1,'java/math/BigDecimal.doRound')
f(32,2813,1,1,'java/math/BigDecimal.precision')
f(33,2813,1,1,'java/math/BigDecimal.longDigitLength')
f(30,2814,10,2,'java/math/BigDecimal.precision',7,0,0)
f(31,2821,3,1,'java/math/BigDecimal.longDigitLength')
f(29,2824,55,1,'java/math/BigInteger.<init>',1,0,0)
f(30,2825,54,1,'java/math/BigInteger.<init>')
f(31,2860,9,2,'java/lang/Integer.parseInt',9,0,0)
f(32,2860,9,2,'java/lang/String.charAt',9,0,0)
f(31,2869,10,2,'java/lang/String.lastIndexOf',10,0,0)
f(32,2869,10,2,'java/lang/String.lastIndexOf',10,0,0)
f(33,2869,10,2,'java/lang/StringLatin1.lastIndexOf',10,0,0)
f(29,2879,1,1,'java/math/BigInteger.or')
f(30,2879,1,2,'java/math/BigInteger.valueOf',1,0,0)
f(31,2879,1,2,'java/math/BigInteger.<init>',1,0,0)
f(28,2880,13,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$Builder.build')
f(29,2880,13,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$Builder.buildPartial')
f(28,2893,2,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$Builder.setSide')
f(28,2895,1,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$Builder.setSymbol')
f(28,2896,2,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.getError')
f(28,2898,2,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.getVenueCode')
f(28,2900,31,2,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.toBuilder',20,0,0)
f(29,2920,11,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$Builder.mergeFrom')
f(30,2920,6,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',6,0,0)
f(30,2926,5,2,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$Builder.mergeCumulativeFillAmount',5,0,0)
f(31,2926,5,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',5,0,0)
f(28,2931,4,1,'io/hydrax/proto/metwo/match/PsOrder.getBaseBalanceAccountId')
f(28,2935,1,1,'io/hydrax/proto/metwo/match/PsOrder.getClOrdId')
f(28,2936,1,1,'io/hydrax/proto/metwo/match/PsOrder.getPrice')
f(28,2937,1,1,'io/hydrax/proto/metwo/match/PsOrder.getQuoteBalanceAccountId')
f(28,2938,1,1,'io/hydrax/proto/metwo/match/PsOrder.getServiceAccountId')
f(28,2939,1,1,'io/hydrax/proto/metwo/match/PsOrder.getTimeInForce')
f(29,2939,1,2,'io/hydrax/proto/metwo/match/TimeInForce.valueOf',1,0,0)
f(30,2939,1,2,'io/hydrax/proto/metwo/match/TimeInForce.forNumber',1,0,0)
f(28,2940,2,1,'io/hydrax/proto/metwo/match/PsOrder.getUserId')
f(28,2942,16,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.build')
f(29,2942,16,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.buildPartial')
f(28,2958,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setAssetHoldingAccountId')
f(28,2959,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setError')
f(29,2959,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(28,2960,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setExecIncludePremiumAveragePrice')
f(29,2960,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(28,2961,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setFromService')
f(28,2962,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setOrderStatus')
f(29,2962,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(28,2963,2,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setProcessedTime')
f(29,2964,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(28,2965,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setRemainingEarmarkFee')
f(29,2965,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(28,2966,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setRemainingEarmarkTax')
f(28,2967,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setSide')
f(28,2968,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.setTradeFeeAmount')
f(29,2968,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(28,2969,2,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.newBuilder')
f(29,2970,1,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.toBuilder')
f(30,2970,1,4,'ClassLoaderData::holder() const')
f(28,2971,2,1,'io/hydrax/proto/metwo/match/Response$Builder.build')
f(29,2971,2,2,'io/hydrax/proto/metwo/match/Response.isInitialized',2,0,0)
f(28,2973,5,1,'io/hydrax/proto/metwo/match/ResponseList$Builder.addResponses')
f(29,2973,5,2,'java/util/ArrayList.add',3,0,0)
f(30,2973,5,2,'java/util/ArrayList.add',3,0,0)
f(31,2973,5,2,'java/util/ArrayList.grow',3,0,0)
f(32,2973,5,2,'java/util/ArrayList.grow',3,0,0)
f(33,2976,2,1,'java/util/Arrays.copyOf')
f(28,2978,5,1,'io/hydrax/proto/metwo/match/ResponseList$Builder.build')
f(29,2978,5,2,'io/hydrax/proto/metwo/match/ResponseList$Builder.buildPartial',5,0,0)
f(28,2983,1,1,'io/hydrax/proto/metwo/match/ResponseList$Builder.setOutSequence')
f(29,2983,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(28,2984,10,2,'io/vertx/core/http/impl/headers/HeadersMultiMap.get',10,0,0)
f(29,2984,10,2,'io/vertx/core/http/impl/headers/HeadersMultiMap.get',10,0,0)
f(30,2984,10,2,'io/vertx/core/http/impl/headers/HeadersMultiMap.get0',10,0,0)
f(28,2994,43,2,'java/util/ImmutableCollections$SetN.contains',43,0,0)
f(29,2994,43,2,'java/util/ImmutableCollections$SetN.probe',43,0,0)
f(28,3037,3,1,'java/util/concurrent/ConcurrentHashMap.get')
f(28,3040,5,1,'java/util/concurrent/atomic/AtomicLong.getAndIncrement')
f(28,3045,2,1,'org/slf4j/impl/Slf4jLogger.isTraceEnabled')
f(29,3045,2,2,'org/jboss/logmanager/Logger.isLoggable',2,0,0)
f(30,3045,2,2,'org/jboss/logmanager/LoggerNode.isLoggableLevel',2,0,0)
f(28,3047,1,4,'os::javaTimeMillis()')
f(27,3048,16,2,'io/hydrax/pricestreaming/events/ExecutionReportEvent_ClientProxy.arc$delegate',15,0,0)
f(28,3048,16,2,'io/quarkus/arc/impl/ClientProxies.getApplicationScopedDelegate',15,0,0)
f(29,3048,16,2,'io/quarkus/arc/impl/AbstractSharedContext.get',15,0,0)
f(30,3060,1,1,'io/quarkus/arc/generator/Default_jakarta_enterprise_context_ApplicationScoped_ContextInstances.getIfPresent')
f(30,3061,3,2,'io/quarkus/arc/impl/Scopes.scopeMatches',3,0,0)
f(22,3064,2,2,'io/vertx/core/impl/ContextInternal.runOnContext',2,0,0)
f(23,3064,2,2,'io/vertx/core/impl/EventLoopExecutor.execute',2,0,0)
f(24,3064,2,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute',2,0,0)
f(25,3064,2,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute0',2,0,0)
f(26,3064,2,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute',2,0,0)
f(27,3064,2,2,'io/netty/channel/nio/NioEventLoop.wakeup',2,0,0)
f(28,3064,2,2,'io/netty/channel/nio/SelectedSelectionKeySetSelector.wakeup',2,0,0)
f(29,3064,2,2,'sun/nio/ch/EPollSelectorImpl.wakeup',2,0,0)
f(30,3064,2,2,'sun/nio/ch/EventFD.set',2,0,0)
f(20,3066,1,4,'os::javaTimeNanos()')
f(17,3067,2,2,'io/vertx/core/eventbus/impl/OutboundDeliveryContext.execute',2,0,0)
f(18,3067,2,2,'io/vertx/core/eventbus/impl/EventBusImpl.sendOrPub',2,0,0)
f(19,3067,2,2,'io/vertx/core/eventbus/impl/EventBusImpl.sendLocally',2,0,0)
f(20,3067,2,2,'io/vertx/core/eventbus/impl/EventBusImpl.deliverMessageLocally',2,0,0)
f(21,3067,2,2,'java/util/concurrent/ConcurrentHashMap.get',2,0,0)
f(16,3069,1,1,'sun/nio/ch/EPollSelectorImpl.clearInterrupt')
f(17,3069,1,1,'sun/nio/ch/EventFD.reset')
f(18,3069,1,1,'sun/nio/ch/IOUtil.drain')
f(19,3069,1,1,'java/lang/invoke/LambdaForm$MH.0x00007f9eb7c85400.invoke')
f(20,3069,1,3,'__clock_gettime')
f(21,3069,1,3,'[vdso]')
f(14,3070,10,2,'io/vertx/core/impl/ContextImpl.duplicate',10,0,0)
f(15,3078,2,3,'__clock_gettime')
f(16,3078,2,3,'[vdso]')
f(10,3080,44,1,'io/vertx/core/impl/ContextInternal$$Lambda.0x00007f9eb75af660.run')
f(11,3080,44,1,'io/vertx/core/impl/ContextInternal.lambda$runOnContext$0',1,0,0)
f(12,3080,44,1,'io/vertx/core/impl/ContextInternal.dispatch',1,0,0)
f(13,3080,44,1,'io/vertx/core/impl/ContextInternal.dispatch',1,0,0)
f(14,3080,43,1,'io/quarkus/vertx/runtime/VertxEventBusConsumerRecorder$3$1$1.handle')
f(15,3081,42,2,'io/quarkus/vertx/runtime/VertxEventBusConsumerRecorder$3$1$1.handle',21,0,0)
f(16,3081,42,2,'io/quarkus/virtual/threads/ContextPreservingExecutorService.execute',21,0,0)
f(17,3081,8,2,'io/quarkus/virtual/threads/ContextPreservingExecutorService.decorate',8,0,0)
f(18,3081,8,2,'io/quarkus/virtual/threads/ContextPreservingExecutorService$ContextPreservingRunnable.<init>',8,0,0)
f(19,3081,8,2,'io/vertx/core/Vertx.currentContext',8,0,0)
f(20,3081,8,2,'io/vertx/core/impl/VertxImpl.currentContext',8,0,0)
f(21,3081,8,2,'java/lang/ThreadLocal.get',8,0,0)
f(22,3081,8,2,'java/lang/ThreadLocal.get',8,0,0)
f(23,3081,8,2,'java/lang/ThreadLocal$ThreadLocalMap.getEntry',8,0,0)
f(24,3081,8,2,'java/lang/ThreadLocal$ThreadLocalMap.getEntryAfterMiss',8,0,0)
f(17,3089,34,2,'java/util/concurrent/ThreadPerTaskExecutor.execute',13,0,0)
f(18,3089,34,2,'java/util/concurrent/ThreadPerTaskExecutor.start',13,0,0)
f(19,3089,22,2,'java/util/concurrent/ThreadPerTaskExecutor.newThread',9,0,0)
f(20,3089,22,2,'java/lang/ThreadBuilders$VirtualThreadFactory.newThread',9,0,0)
f(21,3089,11,1,'java/lang/ThreadBuilders$BaseThreadFactory.nextThreadName',2,0,0)
f(22,3089,2,2,'java/lang/StringBuilder.append',1,0,0)
f(23,3089,2,2,'java/lang/AbstractStringBuilder.append',2,0,0)
f(24,3090,1,2,'java/lang/Long.getChars',1,0,0)
f(22,3091,2,1,'java/lang/StringBuilder.toString')
f(23,3091,1,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(24,3091,1,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(25,3091,1,4,'MemAllocator::allocate() const')
f(26,3091,1,4,'ObjArrayAllocator::initialize(HeapWordImpl**) const')
f(23,3092,1,1,'java/lang/String.<init>')
f(24,3092,1,1,'java/lang/String.<init>')
f(25,3092,1,1,'java/util/Arrays.copyOfRange')
f(26,3092,1,4,'OptoRuntime::new_array_nozero_C(Klass*, int, JavaThread*)')
f(27,3092,1,4,'OptoRuntime::is_deoptimized_caller_frame(JavaThread*)')
f(28,3092,1,4,'CodeHeap::find_blob(void*) const')
f(22,3093,7,1,'java/lang/invoke/VarHandleGuards.guard_LI_J',1,0,0)
f(23,3093,5,1,'java/lang/invoke/LambdaForm$MH.0x00007f9eb7c85400.invoke')
f(24,3094,3,1,'java/lang/invoke/LambdaForm$DMH.0x00007f9eb7c84400.invokeStatic')
f(25,3094,3,1,'java/lang/invoke/VarHandleLongs$FieldInstanceReadWrite.getAndAdd')
f(24,3097,1,1,'java/lang/invoke/LambdaForm$DMH.0x00007f9eb7c84800.invokeStatic')
f(23,3098,2,2,'java/lang/invoke/MethodHandle.asType',1,0,0)
f(24,3099,1,1,'java/lang/invoke/MethodHandle.setAsTypeCache')
f(25,3099,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(21,3100,11,2,'java/lang/ThreadBuilders.newVirtualThread',7,0,0)
f(22,3100,11,2,'java/lang/VirtualThread.<init>',7,0,0)
f(23,3100,11,2,'java/lang/BaseVirtualThread.<init>',7,0,0)
f(24,3100,11,2,'java/lang/Thread.<init>',7,0,0)
f(25,3100,11,2,'java/lang/ThreadLocal.createInheritedMap',7,0,0)
f(26,3107,4,1,'java/lang/ThreadLocal$ThreadLocalMap.<init>')
f(27,3107,1,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(28,3107,1,4,'InstanceKlass::allocate_objArray(int, int, JavaThread*)')
f(27,3108,3,2,'io/quarkus/vertx/mdc/provider/LateBoundMDCProvider$1.childValue',3,0,0)
f(19,3111,12,2,'java/util/concurrent/ThreadPerTaskExecutor.start',4,0,0)
f(20,3111,1,1,'java/lang/System$2.start')
f(21,3111,1,1,'java/lang/VirtualThread.start')
f(22,3111,1,1,'java/lang/VirtualThread.submitRunContinuation')
f(23,3111,1,1,'java/util/concurrent/ForkJoinPool.execute')
f(24,3111,1,1,'java/util/concurrent/ForkJoinPool.poolSubmit')
f(25,3111,1,1,'java/util/concurrent/ForkJoinPool$WorkQueue.push')
f(26,3111,1,1,'java/util/concurrent/ForkJoinPool.signalWork')
f(27,3111,1,1,'java/util/concurrent/locks/LockSupport.unpark')
f(28,3111,1,1,'jdk/internal/misc/Unsafe.unpark')
f(29,3111,1,3,'__pthread_cond_signal')
f(20,3112,11,2,'java/util/concurrent/ConcurrentHashMap$KeySetView.add',4,0,0)
f(21,3116,7,1,'java/util/concurrent/ConcurrentHashMap.putVal')
f(22,3121,2,2,'java/util/concurrent/ConcurrentHashMap.addCount',2,0,0)
f(14,3123,1,3,'itable stub')
f(10,3124,1,3,'itable stub')
f(8,3125,6,2,'io/netty/util/concurrent/SingleThreadEventExecutor.pollTask',6,0,0)
f(9,3125,6,2,'io/netty/util/concurrent/SingleThreadEventExecutor.pollTaskFrom',6,0,0)
f(7,3131,1,1,'io/netty/util/internal/logging/LocationAwareSlf4JLogger.isDebugEnabled')
f(8,3131,1,1,'org/slf4j/impl/Slf4jLogger.isDebugEnabled')
f(9,3131,1,1,'org/jboss/logmanager/Logger.isLoggable')
f(3,3132,1,1,'java/util/concurrent/ThreadPoolExecutor$Worker.run')
f(4,3132,1,1,'java/util/concurrent/ThreadPoolExecutor.runWorker')
f(5,3132,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.run')
f(6,3132,1,1,'java/util/concurrent/FutureTask.runAndReset')
f(7,3132,1,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(8,3132,1,1,'io/hydrax/pricestreaming/task/LiquidityPriceTask$$Lambda.0x00007f9eb75b7dd8.run')
f(9,3132,1,1,'io/hydrax/pricestreaming/task/LiquidityPriceTask.calculateLiquidityPrice')
f(10,3132,1,1,'java/lang/Iterable.forEach')
f(11,3132,1,1,'io/hydrax/pricestreaming/task/LiquidityPriceTask$$Lambda.0x00007f9eb7bc7128.accept')
f(12,3132,1,1,'io/hydrax/pricestreaming/task/LiquidityPriceTask.lambda$calculateLiquidityPrice$0')
f(13,3132,1,1,'io/hydrax/pricestreaming/task/LiquidityPriceTask.calculateByTicker')
f(14,3132,1,1,'io/hydrax/pricestreaming/cache/CumulativeOrderBookCache.get')
f(15,3132,1,2,'java/util/concurrent/ConcurrentHashMap.get',1,0,0)
f(3,3133,9525,1,'org/agrona/concurrent/AgentRunner.run')
f(4,3133,9525,1,'org/agrona/concurrent/AgentRunner.workLoop')
f(5,3152,9506,1,'org/agrona/concurrent/AgentRunner.doWork',659,0,0)
f(6,3387,41,1,'io/aeron/archive/DedicatedModeArchiveConductor$DedicatedModeRecorder.doWork')
f(7,3390,38,1,'io/aeron/archive/ArchiveConductor$Recorder.doWork',7,0,0)
f(8,3390,36,1,'io/aeron/archive/SessionWorker.doWork',7,0,0)
f(9,3396,29,1,'io/aeron/archive/RecordingSession.doWork')
f(10,3397,28,1,'io/aeron/archive/RecordingSession.record')
f(11,3397,28,2,'io/aeron/Image.blockPoll',17,0,0)
f(12,3397,1,2,'io/aeron/Image.activeTermBuffer',1,0,0)
f(12,3398,10,1,'io/aeron/archive/RecordingWriter.onBlock')
f(13,3398,1,2,'java/nio/Buffer.remaining',1,0,0)
f(13,3399,9,1,'sun/nio/ch/FileChannelImpl.write')
f(14,3399,9,2,'sun/nio/ch/FileChannelImpl.writeInternal',6,0,0)
f(15,3399,1,2,'jdk/internal/misc/Blocker.begin',1,0,0)
f(16,3399,1,2,'jdk/internal/misc/Blocker$ForkJoinPools.beginCompensatedBlock',1,0,0)
f(17,3399,1,2,'java/util/concurrent/ForkJoinPool$2.beginCompensatedBlock',1,0,0)
f(15,3400,5,2,'sun/nio/ch/IOUtil.write',2,0,0)
f(16,3400,5,2,'sun/nio/ch/IOUtil.write',2,0,0)
f(17,3400,5,2,'sun/nio/ch/IOUtil.writeFromNativeBuffer',2,0,0)
f(18,3400,5,2,'sun/nio/ch/UnixFileDispatcherImpl.pwrite',2,0,0)
f(19,3402,3,1,'sun/nio/ch/UnixFileDispatcherImpl.pwrite0')
f(20,3402,1,3,'[unknown]')
f(21,3402,1,3,'__pwrite64')
f(20,3403,1,3,'__pwrite64')
f(20,3404,1,3,'fdval')
f(15,3405,3,2,'sun/nio/ch/NativeThreadSet.add',3,0,0)
f(16,3405,3,2,'sun/nio/ch/NativeThread.currentNativeThread',3,0,0)
f(17,3406,2,2,'sun/nio/ch/NativeThread.current0',2,0,0)
f(18,3406,1,3,'Java_sun_nio_ch_NativeThread_current0')
f(18,3407,1,3,'pthread_self@GLIBC_2.2.5')
f(12,3408,17,2,'io/aeron/logbuffer/TermBlockScanner.scan',17,0,0)
f(9,3425,1,4,'os::javaTimeNanos()')
f(8,3426,2,1,'org/agrona/concurrent/status/AtomicCounter.setOrdered')
f(6,3428,5,1,'io/aeron/archive/DedicatedModeArchiveConductor.doWork')
f(7,3429,4,2,'io/aeron/archive/ArchiveConductor.doWork',2,0,0)
f(8,3429,2,2,'io/aeron/archive/ArchiveConductor.invokeAeronInvoker',1,0,0)
f(9,3430,1,1,'org/agrona/concurrent/AgentInvoker.invoke')
f(10,3430,1,1,'io/aeron/ClientConductor.doWork')
f(11,3430,1,2,'java/util/concurrent/locks/ReentrantLock.unlock',1,0,0)
f(12,3430,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.release',1,0,0)
f(13,3430,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.signalNext',1,0,0)
f(8,3431,2,2,'io/aeron/archive/SessionWorker.doWork',1,0,0)
f(9,3431,1,1,'io/aeron/archive/ControlSession.doWork')
f(9,3432,1,3,'itable stub')
f(6,3433,159,1,'io/aeron/cluster/ConsensusModuleAgent.doWork')
f(7,3436,47,1,'io/aeron/cluster/ConsensusAdapter.poll',10,0,0)
f(8,3443,1,3,'__clock_gettime')
f(8,3444,1,3,'clock_gettime@plt')
f(8,3445,37,1,'io/aeron/Subscription.poll')
f(9,3475,7,1,'io/aeron/Image.poll')
f(10,3475,7,2,'io/aeron/logbuffer/TermReader.read',3,0,0)
f(11,3478,4,1,'io/aeron/FragmentAssembler.onFragment')
f(12,3479,3,1,'io/aeron/cluster/ConsensusAdapter.onFragment')
f(13,3479,3,2,'io/aeron/cluster/ConsensusModuleAgent.onAppendPosition',3,0,0)
f(14,3479,3,2,'org/agrona/collections/Int2ObjectHashMap.get',3,0,0)
f(15,3479,3,2,'org/agrona/collections/Int2ObjectHashMap.getMapped',3,0,0)
f(8,3482,1,4,'os::javaTimeMillis()')
f(7,3483,107,1,'io/aeron/cluster/ConsensusModuleAgent.consensusWork')
f(8,3483,27,2,'io/aeron/cluster/ConsensusModuleAdapter.poll',14,0,0)
f(9,3483,27,2,'io/aeron/Subscription.controlledPoll',14,0,0)
f(10,3497,1,4,'SafepointSynchronize::handle_polling_page_exception(JavaThread*)')
f(11,3497,1,4,'CodeCache::find_blob(void*)')
f(10,3498,12,1,'io/aeron/Image.controlledPoll')
f(8,3510,2,1,'io/aeron/cluster/ConsensusModuleAgent.updateLeaderPosition')
f(9,3510,2,1,'io/aeron/cluster/ConsensusModuleAgent.updateLeaderPosition')
f(10,3510,2,1,'io/aeron/cluster/ConsensusModuleAgent.publishCommitPosition')
f(11,3510,2,1,'io/aeron/cluster/ConsensusPublisher.commitPosition')
f(12,3511,1,2,'io/aeron/ExclusivePublication.tryClaim',1,0,0)
f(8,3512,75,1,'io/aeron/cluster/IngressAdapter.poll',11,0,0)
f(9,3512,75,1,'io/aeron/Subscription.controlledPoll',11,0,0)
f(10,3523,64,1,'io/aeron/Image.controlledPoll')
f(11,3562,25,1,'io/aeron/ControlledFragmentAssembler.onFragment')
f(12,3565,22,1,'io/aeron/cluster/IngressAdapter.onFragment')
f(13,3566,12,2,'io/aeron/cluster/ConsensusModuleAgent.onIngressMessage',12,0,0)
f(14,3566,12,2,'io/aeron/cluster/LogPublisher.appendMessage',12,0,0)
f(15,3566,12,2,'io/aeron/ExclusivePublication.offer',12,0,0)
f(16,3566,12,2,'io/aeron/ExclusivePublication.appendUnfragmentedMessage',12,0,0)
f(17,3566,12,2,'org/agrona/AbstractMutableDirectBuffer.putBytes',12,0,0)
f(18,3566,12,2,'sun/misc/Unsafe.copyMemory',12,0,0)
f(19,3566,12,2,'jdk/internal/misc/Unsafe.copyMemory',12,0,0)
f(20,3566,12,2,'jdk/internal/misc/Unsafe.copyMemoryChecks',12,0,0)
f(21,3566,12,2,'jdk/internal/misc/Unsafe.checkPrimitivePointer',12,0,0)
f(22,3566,12,2,'jdk/internal/misc/Unsafe.checkPointer',12,0,0)
f(23,3566,12,2,'jdk/internal/misc/Unsafe.checkOffset',12,0,0)
f(24,3576,1,3,'jlong_disjoint_arraycopy_avx3')
f(24,3577,1,4,'os::javaTimeMillis()')
f(13,3578,3,3,'jbyte_disjoint_arraycopy_avx3')
f(13,3581,4,3,'jint_disjoint_arraycopy_avx3')
f(13,3585,2,3,'jshort_disjoint_arraycopy_avx3')
f(8,3587,3,2,'io/aeron/cluster/WheelTimerService.poll',3,0,0)
f(7,3590,2,2,'io/aeron/cluster/Election.doWork',1,0,0)
f(8,3590,2,2,'io/aeron/cluster/Election.canvass',1,0,0)
f(9,3591,1,4,'SafepointSynchronize::handle_polling_page_exception(JavaThread*)')
f(10,3591,1,4,'ThreadSafepointState::handle_polling_page_exception()')
f(11,3591,1,4,'SafepointMechanism::process(JavaThread*, bool, bool)')
f(12,3591,1,4,'ttyLocker::break_tty_lock_for_safepoint(long)')
f(6,3592,1119,1,'io/aeron/cluster/service/ClusteredServiceAgent.doWork')
f(7,3674,234,2,'io/aeron/cluster/service/BoundedLogAdapter.isDone',234,0,0)
f(7,3908,749,2,'io/aeron/cluster/service/BoundedLogAdapter.poll',409,0,0)
f(8,4261,23,3,'clock_gettime@plt')
f(8,4284,340,1,'io/aeron/Image.boundedControlledPoll')
f(9,4370,254,1,'io/aeron/cluster/service/BoundedLogAdapter.onFragment')
f(10,4371,253,1,'io/aeron/cluster/service/BoundedLogAdapter.onMessage')
f(11,4371,253,1,'io/aeron/cluster/service/ClusteredServiceAgent.onSessionMessage',3,0,0)
f(12,4374,250,1,'io/hydrax/pricestreaming/aeron/service/SORService.onSessionMessage')
f(13,4376,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(14,4376,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(15,4376,1,4,'MemAllocator::allocate() const')
f(13,4377,176,1,'com/google/protobuf/AbstractMessage$Builder.mergeFrom',16,0,0)
f(14,4377,176,1,'com/google/protobuf/AbstractMessageLite$Builder.mergeFrom',16,0,0)
f(15,4377,176,1,'com/google/protobuf/AbstractMessage$Builder.mergeFrom',16,0,0)
f(16,4377,176,1,'com/google/protobuf/AbstractMessage$Builder.mergeFrom',16,0,0)
f(17,4377,176,1,'com/google/protobuf/AbstractMessageLite$Builder.mergeFrom',16,0,0)
f(18,4377,174,1,'com/google/protobuf/AbstractMessage$Builder.mergeFrom',16,0,0)
f(19,4377,174,1,'com/google/protobuf/AbstractMessage$Builder.mergeFrom',16,0,0)
f(20,4377,174,1,'io/hydrax/proto/metwo/match/Request$Builder.mergeFrom',16,0,0)
f(21,4377,174,1,'io/hydrax/proto/metwo/match/Request$Builder.mergeFrom',16,0,0)
f(22,4377,172,1,'io/hydrax/proto/metwo/match/Request$1.parsePartialFrom',16,0,0)
f(23,4377,172,1,'io/hydrax/proto/metwo/match/Request$1.parsePartialFrom',16,0,0)
f(24,4386,156,1,'io/hydrax/proto/metwo/match/Request.<init>')
f(25,4388,142,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readMessage',3,0,0)
f(26,4388,1,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.pushLimit',1,0,0)
f(26,4389,2,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',2,0,0)
f(26,4391,61,1,'io/hydrax/proto/metwo/match/PsOrder$1.parsePartialFrom')
f(27,4391,61,1,'io/hydrax/proto/metwo/match/PsOrder$1.parsePartialFrom')
f(28,4391,1,4,'ClassLoaderData::holder() const')
f(28,4392,60,1,'io/hydrax/proto/metwo/match/PsOrder.<init>')
f(29,4400,30,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readMessage',9,0,0)
f(30,4405,6,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',6,0,0)
f(30,4411,19,1,'io/hydrax/proto/metwo/match/UDec128$1.parsePartialFrom')
f(31,4411,19,2,'io/hydrax/proto/metwo/match/UDec128$1.parsePartialFrom',11,0,0)
f(32,4411,19,2,'io/hydrax/proto/metwo/match/UDec128.<init>',11,0,0)
f(33,4412,8,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readTag',6,0,0)
f(34,4412,8,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',6,0,0)
f(35,4418,2,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(36,4418,2,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(37,4418,2,4,'MemAllocator::allocate() const')
f(38,4419,1,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(33,4420,8,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readUInt64',4,0,0)
f(34,4420,8,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64',4,0,0)
f(35,4424,4,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64SlowPath')
f(33,4428,2,1,'com/google/protobuf/UnknownFieldSet$Builder.build')
f(34,4428,2,2,'java/util/TreeMap$EntrySet.iterator',2,0,0)
f(35,4428,2,2,'java/util/TreeMap.getFirstEntry',2,0,0)
f(29,4430,15,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readStringRequireUtf8',4,0,0)
f(30,4430,4,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',4,0,0)
f(30,4434,11,1,'com/google/protobuf/Utf8.decodeUtf8',2,0,0)
f(31,4434,11,1,'com/google/protobuf/Utf8$UnsafeProcessor.decodeUtf8',2,0,0)
f(32,4435,10,1,'java/lang/String.<init>',1,0,0)
f(33,4436,9,1,'java/lang/String.<init>')
f(34,4443,2,3,'jbyte_disjoint_arraycopy_avx3')
f(29,4445,6,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readTag',5,0,0)
f(30,4445,6,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',5,0,0)
f(31,4450,1,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64SlowPath')
f(29,4451,1,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readUInt64')
f(30,4451,1,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64',1,0,0)
f(26,4452,78,1,'io/hydrax/proto/metwo/match/ResponseList$1.parsePartialFrom')
f(27,4452,78,1,'io/hydrax/proto/metwo/match/ResponseList$1.parsePartialFrom')
f(28,4452,78,1,'io/hydrax/proto/metwo/match/ResponseList.<init>')
f(29,4453,77,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readMessage')
f(30,4453,77,1,'io/hydrax/proto/metwo/match/Response$1.parsePartialFrom')
f(31,4453,77,1,'io/hydrax/proto/metwo/match/Response$1.parsePartialFrom')
f(32,4453,77,1,'io/hydrax/proto/metwo/match/Response.<init>')
f(33,4454,76,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readMessage',5,0,0)
f(34,4454,1,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',1,0,0)
f(34,4455,75,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$1.parsePartialFrom',4,0,0)
f(35,4455,75,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$1.parsePartialFrom',4,0,0)
f(36,4459,71,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.<init>')
f(37,4468,4,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readEnum',4,0,0)
f(38,4468,4,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',4,0,0)
f(37,4472,26,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readMessage',9,0,0)
f(38,4478,5,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',5,0,0)
f(38,4483,14,1,'io/hydrax/proto/metwo/match/UDec128$1.parsePartialFrom')
f(39,4484,13,2,'io/hydrax/proto/metwo/match/UDec128$1.parsePartialFrom',7,0,0)
f(40,4485,12,2,'io/hydrax/proto/metwo/match/UDec128.<init>',6,0,0)
f(41,4487,3,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readTag',2,0,0)
f(42,4487,3,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',2,0,0)
f(43,4489,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(44,4489,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(45,4489,1,4,'MemAllocator::allocate() const')
f(41,4490,6,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readUInt64',2,0,0)
f(42,4490,6,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64',2,0,0)
f(43,4492,4,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64SlowPath')
f(41,4496,1,1,'com/google/protobuf/UnknownFieldSet$Builder.build')
f(42,4496,1,2,'java/util/TreeMap$EntrySet.iterator',1,0,0)
f(43,4496,1,2,'java/util/TreeMap.getFirstEntry',1,0,0)
f(38,4497,1,3,'itable stub')
f(37,4498,24,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readStringRequireUtf8',17,0,0)
f(38,4498,9,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',9,0,0)
f(38,4507,15,2,'com/google/protobuf/Utf8.decodeUtf8',10,0,0)
f(39,4507,15,2,'com/google/protobuf/Utf8$UnsafeProcessor.decodeUtf8',10,0,0)
f(40,4507,15,2,'java/lang/String.<init>',10,0,0)
f(41,4517,5,1,'java/lang/String.<init>')
f(37,4522,5,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readTag',4,0,0)
f(38,4522,5,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',4,0,0)
f(39,4526,1,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64SlowPath')
f(37,4527,1,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readUInt64',1,0,0)
f(38,4527,1,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64',1,0,0)
f(37,4528,1,1,'com/google/protobuf/GeneratedMessageV3.makeExtensionsImmutable')
f(37,4529,1,1,'com/google/protobuf/UnknownFieldSet.newBuilder')
f(38,4529,1,1,'com/google/protobuf/UnknownFieldSet$Builder.access$000')
f(39,4529,1,1,'com/google/protobuf/UnknownFieldSet$Builder.create')
f(40,4529,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(41,4529,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(25,4530,6,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readStringRequireUtf8',2,0,0)
f(26,4530,1,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',1,0,0)
f(26,4531,5,1,'com/google/protobuf/Utf8.decodeUtf8',1,0,0)
f(27,4531,5,1,'com/google/protobuf/Utf8$UnsafeProcessor.decodeUtf8',1,0,0)
f(28,4531,5,1,'java/lang/String.<init>',1,0,0)
f(29,4532,4,1,'java/lang/String.<init>')
f(30,4535,1,1,'java/util/Arrays.copyOfRange')
f(31,4535,1,4,'OptoRuntime::new_array_nozero_C(Klass*, int, JavaThread*)')
f(32,4535,1,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(33,4535,1,4,'MemAllocator::allocate() const')
f(34,4535,1,4,'ParallelScavengeHeap::mem_allocate(unsigned long, bool*)')
f(35,4535,1,4,'VMThread::wait_until_executed(VM_Operation*)')
f(25,4536,2,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readTag',2,0,0)
f(26,4536,2,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint32',2,0,0)
f(25,4538,4,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readUInt64',2,0,0)
f(26,4538,4,2,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64',2,0,0)
f(27,4540,2,1,'com/google/protobuf/CodedInputStream$ArrayDecoder.readRawVarint64SlowPath')
f(24,4542,3,3,'jbyte_disjoint_arraycopy_avx3')
f(24,4545,3,3,'jint_disjoint_arraycopy_avx3')
f(24,4548,1,3,'jshort_disjoint_arraycopy_avx3')
f(22,4549,2,1,'io/hydrax/proto/metwo/match/Request$Builder.mergeFrom')
f(23,4550,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(18,4551,2,1,'com/google/protobuf/CodedInputStream.newInstance')
f(19,4551,2,1,'com/google/protobuf/CodedInputStream.newInstance')
f(20,4551,2,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(21,4551,2,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(22,4551,2,4,'MemAllocator::allocate() const')
f(23,4552,1,3,'memset@plt')
f(13,4553,8,1,'io/hydrax/pricestreaming/cache/SequenceCache.get',2,0,0)
f(14,4555,6,1,'java/util/concurrent/ConcurrentHashMap.computeIfAbsent')
f(15,4560,1,1,'java/lang/String.equals')
f(13,4561,2,2,'io/hydrax/pricestreaming/cache/SequenceCache.put',1,0,0)
f(14,4561,2,2,'io/hydrax/pricestreaming/cache/SequenceCache.getAtomic',1,0,0)
f(15,4562,1,1,'java/util/concurrent/ConcurrentHashMap.computeIfAbsent')
f(13,4563,7,1,'io/hydrax/pricestreaming/utils/IdUtil.formatId')
f(14,4565,2,1,'java/lang/Long.toString')
f(14,4567,3,1,'java/lang/StringBuilder.append')
f(15,4568,2,2,'java/lang/AbstractStringBuilder.append',2,0,0)
f(16,4568,1,2,'java/lang/AbstractStringBuilder.ensureCapacityInternal',1,0,0)
f(17,4568,1,2,'java/util/Arrays.copyOf',1,0,0)
f(16,4569,1,2,'java/lang/Long.getChars',1,0,0)
f(13,4570,2,1,'io/hydrax/proto/metwo/match/Request$Builder.build')
f(14,4570,2,1,'io/hydrax/proto/metwo/match/Request$Builder.buildPartial')
f(13,4572,1,1,'io/hydrax/proto/metwo/match/Request.newBuilder')
f(14,4572,1,1,'io/hydrax/proto/metwo/match/Request.toBuilder')
f(15,4572,1,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(16,4572,1,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(17,4572,1,4,'MemAllocator::allocate() const')
f(18,4572,1,4,'AllocTracer::send_allocation_in_new_tlab(Klass*, HeapWordImpl**, unsigned long, unsigned long, JavaThread*)')
f(19,4572,1,4,'LeakProfiler::is_running()')
f(13,4573,8,2,'io/vertx/core/eventbus/DeliveryOptions.addHeader',5,0,0)
f(14,4573,1,1,'io/vertx/core/eventbus/DeliveryOptions.checkHeaders')
f(15,4573,1,1,'io/vertx/core/MultiMap.caseInsensitiveMultiMap')
f(16,4573,1,1,'io/vertx/core/http/impl/headers/HeadersMultiMap.headers')
f(17,4573,1,1,'io/vertx/core/http/impl/headers/HeadersMultiMap.<init>')
f(18,4573,1,1,'io/vertx/core/http/impl/headers/HeadersMultiMap.<init>')
f(19,4573,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(20,4573,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(21,4573,1,4,'MemAllocator::allocate() const')
f(22,4573,1,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(23,4573,1,4,'ThreadLocalAllocBuffer::retire_before_allocation()')
f(14,4574,7,2,'io/vertx/core/http/impl/headers/HeadersMultiMap.add',5,0,0)
f(15,4574,7,2,'io/vertx/core/http/impl/headers/HeadersMultiMap.add',5,0,0)
f(16,4579,2,1,'io/vertx/core/http/impl/headers/HeadersMultiMap.add')
f(17,4580,1,2,'io/netty/util/AsciiString.hashCode',1,0,0)
f(18,4580,1,2,'io/netty/util/AsciiString.hashCode',1,0,0)
f(19,4580,1,2,'io/netty/util/internal/PlatformDependent.hashCodeAscii',1,0,0)
f(13,4581,15,1,'io/vertx/core/eventbus/impl/EventBusImpl.publish',1,0,0)
f(14,4582,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(15,4582,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(14,4583,13,1,'io/vertx/core/eventbus/impl/EventBusImpl.publish')
f(15,4583,2,2,'io/vertx/core/eventbus/impl/EventBusImpl.createMessage',1,0,0)
f(16,4584,1,1,'io/vertx/core/eventbus/impl/CodecManager.lookupCodec')
f(17,4584,1,2,'io/quarkus/vertx/runtime/VertxEventBusConsumerRecorder$4.apply',1,0,0)
f(15,4585,11,1,'io/vertx/core/eventbus/impl/EventBusImpl.sendOrPubInternal')
f(16,4585,1,1,'io/vertx/core/eventbus/impl/EventBusImpl.newSendContext')
f(17,4585,1,1,'io/vertx/core/impl/VertxImpl.getOrCreateContext')
f(18,4585,1,1,'io/vertx/core/impl/VertxImpl.getContext')
f(19,4585,1,1,'io/vertx/core/impl/VertxImpl.currentContext')
f(20,4585,1,1,'java/lang/ThreadLocal.get')
f(21,4585,1,1,'java/lang/ThreadLocal.get')
f(22,4585,1,1,'java/lang/ThreadLocal.getMap')
f(16,4586,10,1,'io/vertx/core/eventbus/impl/EventBusImpl.sendOrPubInternal')
f(17,4586,10,1,'io/vertx/core/eventbus/impl/OutboundDeliveryContext.next')
f(18,4586,10,1,'io/vertx/core/eventbus/impl/DeliveryContextBase.next')
f(19,4586,10,2,'io/vertx/core/eventbus/impl/OutboundDeliveryContext.execute',9,0,0)
f(20,4586,10,2,'io/vertx/core/eventbus/impl/EventBusImpl.sendOrPub',9,0,0)
f(21,4586,10,2,'io/vertx/core/eventbus/impl/EventBusImpl.sendLocally',9,0,0)
f(22,4586,10,2,'io/vertx/core/eventbus/impl/EventBusImpl.deliverMessageLocally',9,0,0)
f(23,4586,10,2,'io/vertx/core/eventbus/impl/HandlerRegistration.receive',9,0,0)
f(24,4586,10,2,'io/vertx/core/impl/EventLoopExecutor.execute',9,0,0)
f(25,4586,10,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute',9,0,0)
f(26,4586,10,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute0',9,0,0)
f(27,4586,10,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute',9,0,0)
f(28,4586,9,2,'io/netty/channel/nio/NioEventLoop.wakeup',9,0,0)
f(29,4586,9,2,'io/netty/channel/nio/SelectedSelectionKeySetSelector.wakeup',9,0,0)
f(30,4586,9,2,'sun/nio/ch/EPollSelectorImpl.wakeup',9,0,0)
f(31,4586,9,2,'sun/nio/ch/EventFD.set',9,0,0)
f(28,4595,1,1,'io/netty/util/concurrent/SingleThreadEventExecutor.addTask')
f(29,4595,1,1,'io/netty/util/concurrent/SingleThreadEventExecutor.offerTask')
f(30,4595,1,1,'io/netty/util/internal/shaded/org/jctools/queues/MpscUnboundedArrayQueue.offer')
f(31,4595,1,1,'io/netty/util/internal/shaded/org/jctools/queues/BaseMpscLinkedArrayQueue.offer')
f(32,4595,1,1,'io/netty/util/internal/shaded/org/jctools/queues/BaseMpscLinkedArrayQueue.offerSlowPath')
f(13,4596,25,1,'io/vertx/core/eventbus/impl/EventBusImpl.send',1,0,0)
f(14,4596,7,1,'io/vertx/core/eventbus/impl/EventBusImpl.createMessage',1,0,0)
f(15,4597,6,1,'io/vertx/core/eventbus/impl/CodecManager.lookupCodec')
f(16,4597,6,2,'io/quarkus/vertx/runtime/VertxEventBusConsumerRecorder$4.apply',6,0,0)
f(14,4603,18,1,'io/vertx/core/eventbus/impl/EventBusImpl.sendOrPubInternal')
f(15,4603,1,2,'io/vertx/core/eventbus/impl/EventBusImpl.newSendContext',1,0,0)
f(16,4603,1,2,'io/vertx/core/impl/VertxImpl.getOrCreateContext',1,0,0)
f(17,4603,1,2,'io/vertx/core/impl/VertxImpl.getContext',1,0,0)
f(18,4603,1,2,'io/vertx/core/impl/VertxImpl.currentContext',1,0,0)
f(19,4603,1,2,'java/lang/ThreadLocal.get',1,0,0)
f(20,4603,1,2,'java/lang/ThreadLocal.get',1,0,0)
f(21,4603,1,2,'java/lang/ThreadLocal$ThreadLocalMap.getEntry',1,0,0)
f(22,4603,1,2,'java/lang/ThreadLocal$ThreadLocalMap.getEntryAfterMiss',1,0,0)
f(15,4604,17,1,'io/vertx/core/eventbus/impl/EventBusImpl.sendOrPubInternal')
f(16,4604,17,1,'io/vertx/core/eventbus/impl/OutboundDeliveryContext.next')
f(17,4604,17,1,'io/vertx/core/eventbus/impl/DeliveryContextBase.next')
f(18,4604,2,2,'io/vertx/core/eventbus/impl/HandlerRegistration$InboundDeliveryContext.execute',2,0,0)
f(19,4604,2,2,'io/vertx/core/eventbus/impl/MessageConsumerImpl.dispatch',2,0,0)
f(20,4604,2,2,'io/vertx/core/impl/ContextInternal.dispatch',2,0,0)
f(21,4604,2,2,'io/vertx/core/impl/ContextInternal.beginDispatch',2,0,0)
f(22,4604,2,2,'io/vertx/core/impl/VertxImpl.beginDispatch',2,0,0)
f(18,4606,15,2,'io/vertx/core/eventbus/impl/OutboundDeliveryContext.execute',7,0,0)
f(19,4606,15,2,'io/vertx/core/eventbus/impl/EventBusImpl.sendOrPub',7,0,0)
f(20,4606,15,2,'io/vertx/core/eventbus/impl/EventBusImpl.sendLocally',7,0,0)
f(21,4606,15,2,'io/vertx/core/eventbus/impl/EventBusImpl.deliverMessageLocally',7,0,0)
f(22,4609,7,1,'io/vertx/core/eventbus/impl/HandlerRegistration.receive')
f(23,4610,6,2,'io/vertx/core/impl/EventLoopExecutor.execute',6,0,0)
f(24,4610,6,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute',6,0,0)
f(25,4610,6,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute0',6,0,0)
f(26,4610,6,2,'io/netty/util/concurrent/SingleThreadEventExecutor.execute',6,0,0)
f(27,4610,6,2,'io/netty/channel/nio/NioEventLoop.wakeup',6,0,0)
f(28,4610,6,2,'io/netty/channel/nio/SelectedSelectionKeySetSelector.wakeup',6,0,0)
f(29,4610,6,2,'sun/nio/ch/EPollSelectorImpl.wakeup',6,0,0)
f(30,4610,6,2,'sun/nio/ch/EventFD.set',6,0,0)
f(22,4616,4,2,'io/vertx/core/eventbus/impl/MessageImpl.copyBeforeReceive',3,0,0)
f(23,4616,4,2,'io/vertx/core/eventbus/impl/MessageImpl.<init>',3,0,0)
f(24,4619,1,1,'io/vertx/core/http/impl/headers/HeadersMultiMap.entries')
f(25,4619,1,1,'io/vertx/core/MultiMap.entries')
f(22,4620,1,2,'java/util/concurrent/ConcurrentHashMap.get',1,0,0)
f(13,4621,2,2,'org/agrona/AbstractMutableDirectBuffer.getBytes',2,0,0)
f(14,4621,2,2,'sun/misc/Unsafe.copyMemory',2,0,0)
f(15,4621,2,2,'jdk/internal/misc/Unsafe.copyMemory',2,0,0)
f(16,4621,2,2,'jdk/internal/misc/Unsafe.copyMemoryChecks',2,0,0)
f(17,4621,2,2,'jdk/internal/misc/Unsafe.checkPrimitivePointer',2,0,0)
f(18,4621,2,2,'jdk/internal/misc/Unsafe.checkPointer',2,0,0)
f(19,4621,2,2,'jdk/internal/misc/Unsafe.checkOffset',2,0,0)
f(13,4623,1,1,'org/slf4j/impl/Slf4jLogger.trace')
f(8,4624,33,4,'os::javaTimeNanos()')
f(7,4657,53,2,'io/aeron/cluster/service/ClusteredServiceAgent.checkForClockTick',53,0,0)
f(7,4710,1,1,'io/aeron/cluster/service/ClusteredServiceAgent.pollServiceAdapter')
f(8,4710,1,2,'io/aeron/cluster/service/ServiceAdapter.poll',1,0,0)
f(9,4710,1,2,'io/aeron/Subscription.poll',1,0,0)
f(6,4711,2629,1,'io/aeron/driver/DriverConductor.doWork')
f(7,4914,3,3,'clock_gettime@plt')
f(7,4917,13,2,'io/aeron/driver/DriverConductor.freeEndOfLifeResources',13,0,0)
f(7,4930,2401,1,'io/aeron/driver/DriverConductor.trackStreamPositions')
f(8,4944,2139,2,'io/aeron/driver/IpcPublication.updatePublisherPositionAndLimit',2139,0,0)
f(8,7083,208,2,'io/aeron/driver/NetworkPublication.updatePublisherPositionAndLimit',188,0,0)
f(9,7083,20,1,'io/aeron/driver/NetworkPublication.cleanBufferTo')
f(10,7084,19,1,'org/agrona/AbstractMutableDirectBuffer.setMemory')
f(11,7084,19,1,'sun/misc/Unsafe.setMemory')
f(12,7084,19,1,'jdk/internal/misc/Unsafe.setMemory')
f(13,7084,19,1,'jdk/internal/misc/Unsafe.setMemory0')
f(14,7090,9,4,'Copy::fill_to_memory_atomic(void*, unsigned long, unsigned char)')
f(14,7099,4,3,'Unsafe_SetMemory0')
f(9,7103,188,2,'io/aeron/driver/NetworkPublication.hasRequiredReceivers',188,0,0)
f(8,7291,40,1,'io/aeron/driver/PublicationImage.trackRebuild')
f(9,7291,36,1,'io/aeron/driver/LossDetector.scan')
f(10,7291,1,1,'io/aeron/driver/LossDetector.activateGap')
f(11,7291,1,1,'io/aeron/driver/StaticDelayGenerator.shouldFeedbackImmediately')
f(10,7292,35,1,'io/aeron/logbuffer/TermGapScanner.scanForGap')
f(11,7310,17,1,'org/agrona/concurrent/UnsafeBuffer.getIntVolatile')
f(12,7317,10,2,'org/agrona/AbstractMutableDirectBuffer.boundsCheck0',10,0,0)
f(9,7327,4,1,'io/aeron/driver/PublicationImage.cleanBufferTo')
f(10,7327,4,1,'org/agrona/AbstractMutableDirectBuffer.setMemory')
f(11,7327,4,1,'sun/misc/Unsafe.setMemory')
f(12,7327,4,1,'jdk/internal/misc/Unsafe.setMemory')
f(13,7327,4,1,'jdk/internal/misc/Unsafe.setMemory0')
f(14,7327,4,4,'Copy::fill_to_memory_atomic(void*, unsigned long, unsigned char)')
f(7,7331,9,4,'os::javaTimeNanos()')
f(6,7340,1880,1,'io/aeron/driver/Receiver.doWork')
f(7,7421,6,3,'clock_gettime@plt')
f(7,7427,123,2,'io/aeron/driver/DutyCycleTracker.measureAndUpdate',123,0,0)
f(8,7427,123,2,'io/aeron/driver/status/DutyCycleStallTracker.reportMeasurement',123,0,0)
f(7,7550,1,2,'io/aeron/driver/PublicationImage.sendPendingStatusMessage',1,0,0)
f(7,7551,1643,1,'io/aeron/driver/media/DataTransportPoller.pollTransports')
f(8,7561,22,1,'io/aeron/driver/media/DataTransportPoller.poll')
f(9,7561,9,1,'io/aeron/driver/media/ReceiveChannelEndpoint.onDataPacket')
f(10,7561,9,1,'io/aeron/driver/DataPacketDispatcher.onDataPacket')
f(11,7561,9,1,'io/aeron/driver/PublicationImage.insertPacket')
f(12,7562,5,2,'io/aeron/logbuffer/TermRebuilder.insert',5,0,0)
f(13,7562,5,2,'org/agrona/AbstractMutableDirectBuffer.putBytes',5,0,0)
f(14,7562,5,2,'sun/misc/Unsafe.copyMemory',5,0,0)
f(15,7562,5,2,'jdk/internal/misc/Unsafe.copyMemory',5,0,0)
f(16,7562,5,2,'jdk/internal/misc/Unsafe.copyMemoryChecks',5,0,0)
f(17,7562,5,2,'jdk/internal/misc/Unsafe.checkPrimitivePointer',5,0,0)
f(18,7562,5,2,'jdk/internal/misc/Unsafe.checkPointer',5,0,0)
f(19,7562,5,2,'jdk/internal/misc/Unsafe.checkOffset',5,0,0)
f(12,7567,3,3,'jlong_disjoint_arraycopy_avx3')
f(9,7570,13,2,'io/aeron/driver/media/UdpChannelTransport.receive',5,0,0)
f(10,7570,13,2,'sun/nio/ch/DatagramChannelImpl.receive',5,0,0)
f(11,7570,3,2,'java/util/concurrent/locks/ReentrantLock.unlock',3,0,0)
f(12,7570,3,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.release',3,0,0)
f(13,7570,3,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.signalNext',3,0,0)
f(11,7573,10,1,'sun/nio/ch/DatagramChannelImpl.receive',2,0,0)
f(12,7573,10,1,'sun/nio/ch/DatagramChannelImpl.receiveIntoNativeBuffer',2,0,0)
f(13,7575,8,1,'sun/nio/ch/DatagramChannelImpl.receive0')
f(14,7576,1,3,'__libc_recvfrom')
f(14,7577,4,3,'__pthread_disable_asynccancel')
f(14,7581,2,3,'__pthread_enable_asynccancel')
f(8,7583,1611,1,'sun/nio/ch/SelectorImpl.selectNow',8,0,0)
f(9,7591,1603,1,'sun/nio/ch/SelectorImpl.lockAndDoSelect')
f(10,7594,1600,2,'sun/nio/ch/EPollSelectorImpl.doSelect',852,0,0)
f(11,7691,751,1,'sun/nio/ch/EPoll.wait',7,0,0)
f(12,7767,9,3,'Java_sun_nio_ch_EPoll_wait')
f(12,7776,659,3,'[unknown]')
f(13,7776,374,3,'__libc_disable_asynccancel')
f(13,8150,49,3,'__libc_enable_asynccancel')
f(13,8199,236,3,'epoll_wait')
f(12,8435,4,3,'epoll_wait')
f(12,8439,3,3,'epoll_wait@plt')
f(11,8442,5,1,'sun/nio/ch/EPollSelectorImpl.processEvents',1,0,0)
f(12,8443,1,1,'java/util/HashMap.get')
f(13,8443,1,1,'java/util/HashMap.getNode')
f(14,8443,1,1,'java/util/HashMap.hash')
f(15,8443,1,1,'java/lang/Integer.hashCode')
f(12,8444,1,1,'sun/nio/ch/EPoll.getDescriptor')
f(12,8445,1,1,'sun/nio/ch/EPoll.getEvents')
f(12,8446,1,1,'sun/nio/ch/SelectorImpl.processReadyEvents')
f(11,8447,251,2,'sun/nio/ch/EPollSelectorImpl.processUpdateQueue',251,0,0)
f(11,8698,496,2,'sun/nio/ch/SelectorImpl.processDeregisterQueue',496,0,0)
f(7,9194,26,4,'os::javaTimeNanos()')
f(6,9220,1729,1,'io/aeron/driver/Sender.doWork')
f(7,9229,3,2,'io/aeron/driver/DutyCycleTracker.measureAndUpdate',3,0,0)
f(8,9229,3,2,'io/aeron/driver/status/DutyCycleStallTracker.reportMeasurement',3,0,0)
f(7,9232,797,1,'io/aeron/driver/Sender.doSend',100,0,0)
f(8,9317,2,3,'__clock_gettime')
f(8,9319,7,3,'clock_gettime@plt')
f(8,9326,697,1,'io/aeron/driver/NetworkPublication.send')
f(9,9930,27,2,'io/aeron/driver/NetworkPublication.heartbeatMessageCheck',27,0,0)
f(9,9957,52,1,'io/aeron/driver/NetworkPublication.sendData',5,0,0)
f(10,9958,43,1,'io/aeron/driver/media/SendChannelEndpoint.send')
f(11,9958,8,1,'io/aeron/driver/media/ManualSndMultiDestination.send')
f(12,9958,8,1,'io/aeron/driver/media/MultiSndDestination.send',2,0,0)
f(13,9960,6,1,'sun/nio/ch/DatagramChannelImpl.send')
f(14,9960,6,2,'sun/nio/ch/DatagramChannelImpl.send',2,0,0)
f(15,9960,6,2,'sun/nio/ch/DatagramChannelImpl.sendFromNativeBuffer',2,0,0)
f(16,9962,4,1,'sun/nio/ch/DatagramChannelImpl.send0')
f(17,9962,2,3,'__pthread_disable_asynccancel')
f(17,9964,2,3,'__pthread_enable_asynccancel')
f(11,9966,35,2,'sun/nio/ch/DatagramChannelImpl.write',15,0,0)
f(12,9966,3,2,'java/util/concurrent/locks/ReentrantLock.unlock',3,0,0)
f(13,9966,3,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.release',3,0,0)
f(14,9966,3,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.signalNext',3,0,0)
f(12,9969,32,2,'sun/nio/ch/IOUtil.write',12,0,0)
f(13,9969,32,2,'sun/nio/ch/IOUtil.write',12,0,0)
f(14,9969,32,2,'sun/nio/ch/IOUtil.writeFromNativeBuffer',12,0,0)
f(15,9969,32,2,'sun/nio/ch/DatagramDispatcher.write',12,0,0)
f(16,9981,20,1,'sun/nio/ch/DatagramDispatcher.write0')
f(17,9982,1,4,'JavaThread::check_special_condition_for_native_trans(JavaThread*)')
f(18,9982,1,4,'SafepointMechanism::process(JavaThread*, bool, bool)')
f(19,9982,1,4,'SafepointSynchronize::block(JavaThread*)')
f(17,9983,7,3,'__pthread_disable_asynccancel')
f(17,9990,4,3,'__pthread_enable_asynccancel')
f(17,9994,1,3,'fdval')
f(17,9995,6,3,'send')
f(10,10001,6,2,'io/aeron/logbuffer/TermScanner.scanForAvailability',4,0,0)
f(11,10005,2,1,'io/aeron/logbuffer/FrameDescriptor.isPaddingFrame')
f(12,10006,1,2,'org/agrona/AbstractMutableDirectBuffer.getShort',1,0,0)
f(13,10006,1,2,'sun/misc/Unsafe.getShort',1,0,0)
f(10,10007,2,1,'org/agrona/concurrent/status/UnsafeBufferPosition.setOrdered')
f(9,10009,14,2,'io/aeron/driver/RetransmitHandler.processTimeouts',14,0,0)
f(8,10023,6,4,'os::javaTimeNanos()')
f(7,10029,920,1,'io/aeron/driver/media/ControlTransportPoller.pollTransports',28,0,0)
f(8,10030,3,1,'io/aeron/driver/media/ControlTransportPoller.poll')
f(9,10030,3,1,'io/aeron/driver/media/UdpChannelTransport.receive')
f(10,10030,3,1,'sun/nio/ch/DatagramChannelImpl.receive')
f(11,10030,2,1,'sun/nio/ch/DatagramChannelImpl.receive')
f(12,10030,2,1,'sun/nio/ch/DatagramChannelImpl.receiveIntoNativeBuffer')
f(13,10030,2,1,'sun/nio/ch/DatagramChannelImpl.receive0')
f(14,10030,1,3,'__libc_recvfrom')
f(14,10031,1,3,'__pthread_disable_asynccancel')
f(11,10032,1,1,'sun/nio/ch/DatagramChannelImpl.sourceSocketAddress')
f(12,10032,1,2,'sun/nio/ch/NativeSocketAddress.decode',1,0,0)
f(13,10032,1,3,'jint_conjoint_arraycopy_avx3')
f(8,10033,916,1,'sun/nio/ch/SelectorImpl.selectNow',27,0,0)
f(9,10060,889,1,'sun/nio/ch/SelectorImpl.lockAndDoSelect')
f(10,10068,881,2,'sun/nio/ch/EPollSelectorImpl.doSelect',464,0,0)
f(11,10119,419,1,'sun/nio/ch/EPoll.wait',3,0,0)
f(12,10154,3,3,'Java_sun_nio_ch_EPoll_wait')
f(12,10157,375,3,'[unknown]')
f(13,10157,240,3,'__libc_disable_asynccancel')
f(13,10397,28,3,'__libc_enable_asynccancel')
f(13,10425,107,3,'epoll_wait')
f(12,10532,4,3,'epoll_wait')
f(12,10536,2,3,'epoll_wait@plt')
f(11,10538,2,2,'sun/nio/ch/EPollSelectorImpl.processEvents',1,0,0)
f(12,10539,1,1,'java/util/HashMap.get')
f(13,10539,1,2,'java/util/HashMap.getNode',1,0,0)
f(11,10540,127,2,'sun/nio/ch/EPollSelectorImpl.processUpdateQueue',127,0,0)
f(12,10666,1,2,'java/lang/Integer.valueOf',1,0,0)
f(11,10667,282,2,'sun/nio/ch/SelectorImpl.processDeregisterQueue',282,0,0)
f(6,10949,86,1,'io/hydrax/aeron/connection/KeepAliveAgent.doWork')
f(7,10953,82,1,'io/hydrax/aeron/connection/KeepAliveProcessor.process',4,0,0)
f(8,10954,2,3,'__clock_gettime')
f(8,10956,4,1,'io/aeron/cluster/client/AeronCluster.pollEgress')
f(9,10956,4,1,'io/aeron/Subscription.poll')
f(8,10960,74,1,'org/agrona/concurrent/SleepingIdleStrategy.idle')
f(9,10960,74,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(10,10960,74,1,'jdk/internal/misc/Unsafe.park')
f(11,10961,25,3,'Unsafe_Park')
f(12,10964,9,4,'Parker::park(bool, long)')
f(13,10968,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<544868ul, CardTableBarrierSet>, (AccessInternal::BarrierType)2, 544868ul>::oop_access_barrier(void*)')
f(13,10969,2,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<548964ul, CardTableBarrierSet>, (AccessInternal::BarrierType)2, 548964ul>::oop_access_barrier(void*)')
f(13,10971,1,4,'JavaThread::is_interrupted(bool)')
f(13,10972,1,4,'java_lang_Thread::interrupted(oopDesc*)')
f(12,10973,1,4,'ThreadInVMfromNative::ThreadInVMfromNative(JavaThread*)')
f(12,10974,5,3,'__lll_unlock_wake')
f(12,10979,2,3,'__pthread_mutex_trylock')
f(12,10981,4,3,'__tls_get_addr')
f(12,10985,1,4,'java_lang_Thread::set_thread_status(oopDesc*, JavaThreadStatus)')
f(11,10986,44,3,'[unknown]')
f(12,10986,2,3,'[unknown]')
f(13,10986,2,3,'__clock_gettime')
f(14,10986,2,3,'[vdso]')
f(12,10988,4,3,'__clock_gettime')
f(13,10989,3,3,'[vdso]')
f(12,10992,3,3,'__condvar_confirm_wakeup')
f(12,10995,13,3,'__pthread_disable_asynccancel')
f(12,11008,1,3,'__pthread_enable_asynccancel')
f(12,11009,2,3,'__pthread_mutex_cond_lock')
f(12,11011,19,3,'pthread_cond_timedwait@@GLIBC_2.3.2')
f(11,11030,3,3,'__condvar_cancel_waiting')
f(11,11033,1,3,'__pthread_mutex_trylock')
f(8,11034,1,4,'os::javaTimeMillis()')
f(6,11035,424,3,'itable stub')
f(6,11459,115,1,'org/agrona/concurrent/BackoffIdleStrategy.idle')
f(7,11468,106,1,'org/agrona/concurrent/BackoffIdleStrategy.idle',17,0,0)
f(8,11468,51,1,'java/lang/Thread.yield',16,0,0)
f(9,11470,49,1,'java/lang/Thread.yield0',14,0,0)
f(10,11497,13,3,'__GI_sched_yield')
f(10,11510,8,3,'__clock_gettime')
f(11,11511,7,3,'[vdso]')
f(10,11518,1,4,'os::naked_yield()')
f(8,11519,55,1,'java/util/concurrent/locks/LockSupport.parkNanos',1,0,0)
f(9,11520,54,1,'jdk/internal/misc/Unsafe.park')
f(10,11523,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<548964ul, CardTableBarrierSet>, (AccessInternal::BarrierType)2, 548964ul>::oop_access_barrier(void*)')
f(10,11524,3,4,'JavaFrameAnchor::make_walkable()')
f(10,11527,18,3,'Unsafe_Park')
f(11,11530,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<286822ul, CardTableBarrierSet>, (AccessInternal::BarrierType)3, 286822ul>::oop_access_barrier(oopDesc*, long)')
f(11,11531,5,4,'Parker::park(bool, long)')
f(12,11533,1,4,'HandshakeState::has_operation(bool, bool)')
f(13,11533,1,3,'__tls_get_addr')
f(12,11534,1,4,'SafepointMechanism::process(JavaThread*, bool, bool)')
f(13,11534,1,4,'ttyLocker::break_tty_lock_for_safepoint(long)')
f(12,11535,1,4,'java_lang_Thread::interrupted(oopDesc*)')
f(11,11536,2,4,'ThreadInVMfromNative::ThreadInVMfromNative(JavaThread*)')
f(11,11538,1,3,'__lll_unlock_wake')
f(11,11539,1,3,'__pthread_mutex_trylock')
f(11,11540,3,3,'__pthread_mutex_unlock_usercnt')
f(11,11543,1,3,'__tls_get_addr')
f(11,11544,1,3,'pthread_mutex_unlock@plt')
f(10,11545,23,3,'[unknown]')
f(11,11545,1,3,'[unknown]')
f(12,11545,1,3,'__clock_gettime')
f(13,11545,1,3,'[vdso]')
f(11,11546,2,3,'__clock_gettime')
f(12,11546,2,3,'[vdso]')
f(11,11548,2,3,'__condvar_confirm_wakeup')
f(11,11550,2,3,'__condvar_release_lock')
f(11,11552,2,3,'__pthread_disable_asynccancel')
f(11,11554,3,3,'__pthread_mutex_unlock_usercnt')
f(11,11557,11,3,'pthread_cond_timedwait@@GLIBC_2.3.2')
f(10,11568,1,3,'__condvar_cancel_waiting')
f(10,11569,1,3,'__pthread_mutex_trylock')
f(10,11570,3,3,'clock_gettime')
f(10,11573,1,4,'java_lang_Thread::set_thread_status(oopDesc*, JavaThreadStatus)')
f(6,11574,1078,1,'org/agrona/concurrent/BusySpinIdleStrategy.idle')
f(6,12652,2,1,'org/agrona/concurrent/SleepingIdleStrategy.idle')
f(6,12654,4,1,'org/agrona/concurrent/SleepingMillisIdleStrategy.idle')
f(7,12654,4,1,'java/lang/Thread.sleep')
f(8,12654,4,1,'java/lang/Thread.sleep0')
f(9,12654,2,3,'JVM_Sleep')
f(10,12654,2,4,'JavaThread::sleep_nanos(long)')
f(11,12655,1,3,'__lll_unlock_wake')
f(9,12656,2,3,'[unknown]')
f(10,12656,2,3,'pthread_cond_timedwait@@GLIBC_2.3.2')
f(1,12658,3850,1,'java/util/concurrent/ForkJoinWorkerThread.run')
f(2,12658,3850,1,'java/util/concurrent/ForkJoinPool.runWorker',0,0,7)
f(3,12667,70,1,'java/util/concurrent/ForkJoinPool.awaitWork',7,0,0)
f(4,12683,1,2,'java/util/concurrent/ForkJoinPool.hasTasks',1,0,0)
f(4,12684,34,1,'java/util/concurrent/locks/LockSupport.park')
f(5,12684,34,1,'jdk/internal/misc/Unsafe.park')
f(6,12687,3,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<548964ul, CardTableBarrierSet>, (AccessInternal::BarrierType)2, 548964ul>::oop_access_barrier(void*)')
f(6,12690,12,3,'Unsafe_Park')
f(7,12692,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<286822ul, CardTableBarrierSet>, (AccessInternal::BarrierType)3, 286822ul>::oop_access_barrier(oopDesc*, long)')
f(7,12693,8,4,'Parker::park(bool, long)')
f(8,12698,2,4,'HandshakeState::has_operation(bool, bool)')
f(9,12698,2,3,'__tls_get_addr')
f(8,12700,1,4,'SafepointMechanism::process(JavaThread*, bool, bool)')
f(9,12700,1,4,'SafepointSynchronize::block(JavaThread*)')
f(7,12701,1,3,'__pthread_mutex_unlock_usercnt')
f(6,12702,1,3,'__pthread_disable_asynccancel')
f(6,12703,1,3,'__pthread_enable_asynccancel')
f(6,12704,1,3,'__pthread_mutex_cond_lock')
f(6,12705,13,3,'pthread_cond_wait@@GLIBC_2.3.2')
f(4,12718,19,2,'java/util/concurrent/locks/LockSupport.parkUntil',16,0,0)
f(5,12734,3,1,'jdk/internal/misc/Unsafe.park')
f(6,12734,2,3,'Unsafe_Park')
f(7,12734,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<286822ul, CardTableBarrierSet>, (AccessInternal::BarrierType)3, 286822ul>::oop_access_barrier(oopDesc*, long)')
f(7,12735,1,3,'pthread_cond_timedwait@@GLIBC_2.3.2')
f(6,12736,1,3,'[unknown]')
f(7,12736,1,3,'pthread_cond_timedwait@@GLIBC_2.3.2')
f(3,12737,3771,1,'java/util/concurrent/ForkJoinPool.scan',14,0,0)
f(4,12741,3738,1,'java/util/concurrent/ForkJoinPool$WorkQueue.topLevelExec',23,0,0)
f(5,12747,3,2,'java/util/concurrent/ForkJoinPool$WorkQueue.tryPoll',3,0,0)
f(6,12749,1,2,'java/util/concurrent/ForkJoinPool$WorkQueue.casSlotToNull',1,0,0)
f(5,12750,3729,1,'java/util/concurrent/ForkJoinTask.doExec',14,0,0)
f(6,12771,3708,1,'java/util/concurrent/ForkJoinTask$RunnableExecuteAction.exec',6,0,0)
f(7,12771,3708,1,'java/lang/VirtualThread$$Lambda.0x00007f9eb7c45588.run',6,0,0)
f(8,12777,3702,1,'java/lang/VirtualThread.runContinuation',0,0,26)
f(9,12811,1,1,'I2C/C2I adapters')
f(9,12812,2,1,'java/lang/Thread.isVirtual')
f(9,12814,1,1,'java/lang/VirtualThread.afterDone')
f(10,12814,1,2,'java/lang/VirtualThread.afterDone',1,0,0)
f(9,12815,1,1,'java/lang/VirtualThread.compareAndSetState')
f(9,12816,5,1,'java/lang/VirtualThread.mount')
f(10,12818,3,4,'SharedRuntime::notify_jvmti_vthread_mount(oopDesc*, unsigned char, JavaThread*)')
f(11,12818,2,4,'JvmtiVTMSTransitionDisabler::start_VTMS_transition(_jobject*, bool)')
f(11,12820,1,3,'__tls_get_addr')
f(9,12821,4,1,'java/lang/VirtualThread.unmount',1,0,0)
f(10,12824,1,4,'SharedRuntime::notify_jvmti_vthread_unmount(oopDesc*, unsigned char, JavaThread*)')
f(11,12824,1,4,'JvmtiVTMSTransitionDisabler::finish_VTMS_transition(_jobject*, bool)')
f(9,12825,3654,1,'jdk/internal/vm/Continuation.run')
f(10,12830,2,2,'jdk/internal/vm/Continuation.currentCarrierThread',2,0,0)
f(11,12830,2,2,'java/lang/System$2.currentCarrierThread',2,0,0)
f(10,12832,3646,1,'jdk/internal/vm/Continuation.enterSpecial')
f(11,12832,3646,1,'jdk/internal/vm/Continuation.enter')
f(12,12834,3644,1,'jdk/internal/vm/Continuation.enter0',21,0,0)
f(13,12834,3644,1,'java/lang/VirtualThread$VThreadContinuation$1.run',21,0,0)
f(14,12834,3644,1,'java/lang/VirtualThread.run',21,0,0)
f(15,12836,34,4,'SharedRuntime::notify_jvmti_vthread_end(oopDesc*, unsigned char, JavaThread*)')
f(16,12836,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<548964ul, CardTableBarrierSet>, (AccessInternal::BarrierType)0, 548964ul>::oop_access_barrier(void*, oopDesc*)')
f(16,12837,1,4,'JNIHandleBlock::allocate_handle(JavaThread*, oopDesc*, AllocFailStrategy::AllocFailEnum)')
f(16,12838,1,4,'JavaThread::rebind_to_jvmti_thread_state_of(oopDesc*)')
f(16,12839,30,4,'JvmtiVTMSTransitionDisabler::VTMS_vthread_end(_jobject*)')
f(17,12839,26,4,'JvmtiExport::cleanup_thread(JavaThread*)')
f(18,12839,22,4,'JvmtiEventController::thread_ended(JavaThread*)')
f(19,12839,2,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<548964ul, CardTableBarrierSet>, (AccessInternal::BarrierType)2, 548964ul>::oop_access_barrier(void*)')
f(19,12841,1,4,'JvmtiEnvThreadState::~JvmtiEnvThreadState()')
f(19,12842,16,4,'JvmtiThreadState::~JvmtiThreadState()')
f(20,12847,2,4,'JvmtiEnvThreadState::~JvmtiEnvThreadState()')
f(20,12849,1,4,'OopStorage::Block::release_entries(unsigned long, OopStorage*)')
f(20,12850,4,4,'OopStorage::release(oopDesc* const*)')
f(21,12853,1,4,'OopStorage::Block::release_entries(unsigned long, OopStorage*)')
f(20,12854,4,3,'_SafeFetchN_fault')
f(19,12858,2,3,'__GI___libc_free')
f(19,12860,1,3,'_int_free')
f(18,12861,4,4,'Mutex::lock(Thread*)')
f(19,12861,2,4,'JavaThread::is_active_Java_thread() const')
f(19,12863,1,3,'__lll_lock_wait')
f(19,12864,1,3,'pthread_mutex_lock')
f(17,12865,1,4,'JvmtiVTMSTransitionDisabler::start_VTMS_transition(_jobject*, bool)')
f(17,12866,3,4,'java_lang_Thread::set_is_in_VTMS_transition(oopDesc*, bool)')
f(16,12869,1,3,'__lll_unlock_wake')
f(15,12870,7,3,'__pthread_mutex_trylock')
f(15,12877,2,3,'_int_free')
f(15,12879,3599,1,'java/lang/Thread.runWith',20,0,0)
f(16,12880,3598,1,'java/util/concurrent/ThreadPerTaskExecutor$TaskRunner.run',19,0,0)
f(17,12880,3587,1,'io/quarkus/virtual/threads/ContextPreservingExecutorService$ContextPreservingRunnable.run',18,0,0)
f(18,12880,3511,1,'io/quarkus/vertx/runtime/VertxEventBusConsumerRecorder$3$1$1$1.run',16,0,0)
f(19,12896,3495,1,'io/quarkus/vertx/runtime/EventConsumerInvoker.invoke')
f(20,12896,6,1,'io/quarkus/arc/ManagedContext.activate')
f(21,12896,6,1,'io/quarkus/arc/impl/RequestContext.activate')
f(22,12896,6,2,'io/quarkus/vertx/runtime/VertxCurrentContextFactory$VertxCurrentContext.set',2,0,0)
f(23,12896,2,2,'io/quarkus/vertx/core/runtime/context/VertxContextSafetyToggle.setContextSafe',2,0,0)
f(24,12896,2,2,'io/vertx/core/impl/ContextInternal.getLocal',2,0,0)
f(25,12896,2,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.getLocal',2,0,0)
f(26,12896,2,2,'java/util/concurrent/ConcurrentHashMap.get',2,0,0)
f(23,12898,4,1,'io/vertx/core/impl/ContextInternal.putLocal')
f(24,12898,4,1,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.putLocal')
f(25,12898,4,1,'java/util/concurrent/ConcurrentHashMap.put')
f(26,12898,4,1,'java/util/concurrent/ConcurrentHashMap.putVal')
f(27,12901,1,1,'java/lang/String.hashCode')
f(20,12902,6,2,'io/quarkus/arc/ManagedContext.terminate',6,0,0)
f(21,12902,1,2,'io/quarkus/arc/impl/RequestContext.deactivate',1,0,0)
f(22,12902,1,2,'io/quarkus/vertx/runtime/VertxCurrentContextFactory$VertxCurrentContext.remove',1,0,0)
f(23,12902,1,2,'io/smallrye/common/vertx/VertxContext.isDuplicatedContext',1,0,0)
f(21,12903,5,2,'io/quarkus/arc/impl/RequestContext.destroy',5,0,0)
f(22,12903,5,2,'io/quarkus/vertx/runtime/VertxCurrentContextFactory$VertxCurrentContext.get',5,0,0)
f(23,12906,2,2,'io/vertx/core/impl/ContextInternal.getLocal',2,0,0)
f(24,12906,2,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.getLocal',2,0,0)
f(25,12906,2,2,'java/util/concurrent/ConcurrentHashMap.get',2,0,0)
f(20,12908,8,2,'io/quarkus/arc/impl/RequestContext.isActive',8,0,0)
f(21,12908,8,2,'io/quarkus/vertx/runtime/VertxCurrentContextFactory$VertxCurrentContext.get',8,0,0)
f(22,12908,8,2,'io/vertx/core/impl/ContextInternal.getLocal',8,0,0)
f(23,12908,8,2,'io/quarkus/vertx/core/runtime/VertxLocalsHelper.getLocal',8,0,0)
f(24,12908,3,2,'io/vertx/core/impl/ContextInternal.localContextData',3,0,0)
f(25,12908,3,2,'io/vertx/core/spi/context/storage/ContextLocal.get',3,0,0)
f(26,12908,3,2,'io/vertx/core/spi/context/storage/ContextLocal.get',3,0,0)
f(27,12908,3,2,'io/vertx/core/impl/ContextBase.getLocal',3,0,0)
f(28,12908,3,2,'io/vertx/core/spi/context/storage/AccessMode$1.getOrCreate',3,0,0)
f(24,12911,5,2,'java/util/concurrent/ConcurrentHashMap.get',5,0,0)
f(20,12916,3475,1,'io/quarkus/vertx/runtime/EventConsumerInvoker.invokeBean',3,0,0)
f(21,12917,3472,1,'io/hydrax/pricestreaming/events/OrderEvent_onOrder_LazyInvoker_zsLLlo-3_nOCSI0S19BT2Tfi_js.invoke')
f(22,12917,3472,1,'io/hydrax/pricestreaming/events/OrderEvent_onOrder_Invoker_zsLLlo-3_nOCSI0S19BT2Tfi_js.invoke',133,0,0)
f(23,12918,3470,1,'io/hydrax/pricestreaming/events/OrderEvent_ClientProxy.onOrder',132,0,0)
f(24,12918,3466,1,'io/hydrax/pricestreaming/events/OrderEvent.onOrder',128,0,0)
f(25,12918,3466,1,'io/hydrax/pricestreaming/service/OrderService_ClientProxy.placeOrder',128,0,0)
f(26,12918,3461,1,'io/hydrax/pricestreaming/service/OrderService.placeOrder',123,0,0)
f(27,12918,1827,1,'io/hydrax/pricestreaming/router/RouterConfig_ProducerMethod_routerConfig_4vyyNmvASOnLZYE6fnI0WWykPSw_ClientProxy.route',115,0,0)
f(28,12918,1,2,'io/hydrax/pricestreaming/router/RouterConfig_ProducerMethod_routerConfig_4vyyNmvASOnLZYE6fnI0WWykPSw_ClientProxy.arc$delegate',1,0,0)
f(29,12918,1,2,'io/quarkus/arc/impl/ClientProxies.getApplicationScopedDelegate',1,0,0)
f(30,12918,1,2,'io/quarkus/arc/impl/AbstractSharedContext.get',1,0,0)
f(31,12918,1,2,'io/quarkus/arc/impl/Scopes.scopeMatches',1,0,0)
f(28,12919,1826,1,'io/hydrax/pricestreaming/router/RoutingEngine.route',114,0,0)
f(29,12919,107,2,'io/hydrax/pricestreaming/config/RouterConfig$$Lambda.0x00007f9eb7c8ee18.get',107,0,0)
f(30,12919,107,2,'io/hydrax/pricestreaming/cache/OrderRoutingStrategyCache.getAll',107,0,0)
f(31,12919,107,2,'java/util/ArrayList.<init>',107,0,0)
f(32,12919,107,2,'java/util/concurrent/ConcurrentHashMap$CollectionView.toArray',107,0,0)
f(33,12932,86,2,'java/util/concurrent/ConcurrentHashMap$ValueIterator.next',86,0,0)
f(34,12932,86,2,'java/util/concurrent/ConcurrentHashMap$Traverser.advance',86,0,0)
f(33,13018,4,2,'java/util/concurrent/ConcurrentHashMap$ValuesView.iterator',4,0,0)
f(34,13018,4,2,'java/util/concurrent/ConcurrentHashMap$ValueIterator.<init>',4,0,0)
f(35,13018,4,2,'java/util/concurrent/ConcurrentHashMap$BaseIterator.<init>',4,0,0)
f(36,13018,4,2,'java/util/concurrent/ConcurrentHashMap$Traverser.advance',4,0,0)
f(33,13022,4,2,'java/util/concurrent/ConcurrentHashMap.mappingCount',4,0,0)
f(29,13026,1378,1,'io/hydrax/pricestreaming/router/Rule.handle')
f(30,13028,26,1,'io/hydrax/pricestreaming/router/Rule.getRouterHandler',3,0,0)
f(31,13028,26,1,'io/hydrax/pricestreaming/router/RouterHandlerFactory.getRouterHandler',3,0,0)
f(32,13028,14,1,'io/hydrax/pricestreaming/common/RoutingStrategyType.fromType',2,0,0)
f(33,13028,14,1,'java/util/stream/ReferencePipeline.findFirst',2,0,0)
f(34,13028,14,1,'java/util/stream/AbstractPipeline.evaluate',2,0,0)
f(35,13028,14,1,'java/util/stream/FindOps$FindOp.evaluateSequential',2,0,0)
f(36,13028,14,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',2,0,0)
f(37,13028,12,1,'java/util/stream/AbstractPipeline.copyInto')
f(38,13028,12,1,'java/util/stream/AbstractPipeline.copyIntoWithCancel')
f(39,13028,2,1,'java/util/Spliterator.getExactSizeIfKnown')
f(40,13029,1,3,'itable stub')
f(39,13030,10,1,'java/util/stream/ReferencePipeline.forEachWithCancel')
f(40,13030,10,1,'java/util/Spliterators$ArraySpliterator.tryAdvance')
f(41,13031,9,1,'java/util/stream/ReferencePipeline$2$1.accept')
f(42,13034,4,1,'io/hydrax/pricestreaming/common/RoutingStrategyType$$Lambda.0x00007f9eb7c8fac8.test')
f(43,13036,2,2,'io/hydrax/pricestreaming/common/RoutingStrategyType.lambda$fromType$0',2,0,0)
f(44,13036,2,2,'java/lang/String.equals',2,0,0)
f(42,13038,1,3,'itable stub')
f(42,13039,1,1,'java/util/stream/FindOps$FindSink.accept')
f(37,13040,2,2,'java/util/stream/AbstractPipeline.wrapSink',2,0,0)
f(32,13042,12,1,'java/util/Optional.map',1,0,0)
f(33,13042,12,1,'io/hydrax/pricestreaming/router/RouterHandlerFactory$$Lambda.0x00007f9eb7c8fd10.apply',1,0,0)
f(34,13042,12,1,'io/hydrax/pricestreaming/router/RouterHandlerFactory.lambda$getRouterHandler$0',1,0,0)
f(35,13042,12,1,'io/hydrax/pricestreaming/utils/BeanUtil.getBean',1,0,0)
f(36,13042,12,1,'io/quarkus/arc/impl/ArcContainerImpl.instance',1,0,0)
f(37,13042,12,1,'io/quarkus/arc/impl/ArcContainerImpl.instanceHandle',1,0,0)
f(38,13042,3,2,'io/quarkus/arc/impl/ArcContainerImpl.beanInstanceHandle',1,0,0)
f(39,13042,3,2,'io/quarkus/arc/impl/ArcContainerImpl.beanInstanceHandle',1,0,0)
f(40,13042,3,2,'io/quarkus/arc/impl/ArcContainerImpl.beanInstanceHandle',1,0,0)
f(41,13043,2,1,'io/hydrax/pricestreaming/router/SequenceStrategy_Bean.get')
f(42,13043,2,1,'io/hydrax/pricestreaming/router/SequenceStrategy_Bean.get')
f(43,13043,2,1,'io/quarkus/arc/impl/AbstractSharedContext.get')
f(44,13043,1,2,'io/quarkus/arc/impl/ComputingCacheContextInstances.computeIfAbsent',1,0,0)
f(45,13043,1,2,'io/quarkus/arc/impl/ComputingCache.computeIfAbsent',1,0,0)
f(46,13043,1,2,'java/util/concurrent/ConcurrentHashMap.get',1,0,0)
f(44,13044,1,2,'io/quarkus/arc/impl/Scopes.scopeMatches',1,0,0)
f(38,13045,9,1,'io/quarkus/arc/impl/ArcContainerImpl.getBean')
f(39,13049,5,2,'io/quarkus/arc/impl/ComputingCache.getValue',5,0,0)
f(40,13049,5,2,'io/quarkus/arc/impl/ComputingCache.computeIfAbsent',5,0,0)
f(41,13049,5,2,'io/quarkus/arc/impl/ComputingCache.computeIfAbsent',5,0,0)
f(42,13049,5,2,'java/util/concurrent/ConcurrentHashMap.get',5,0,0)
f(43,13053,1,2,'io/quarkus/arc/impl/ArcContainerImpl$Resolvable.equals',1,0,0)
f(44,13053,1,2,'java/util/Arrays.equals',1,0,0)
f(45,13053,1,2,'java/util/Objects.equals',1,0,0)
f(30,13054,1350,1,'io/hydrax/pricestreaming/router/SequenceStrategy.handle')
f(31,13054,3,2,'io/hydrax/pricestreaming/cache/SequenceVenueCache.get',3,0,0)
f(32,13054,3,2,'java/util/concurrent/ConcurrentHashMap.get',3,0,0)
f(31,13057,1335,1,'java/util/Optional.ifPresentOrElse',12,0,0)
f(32,13057,1335,1,'io/hydrax/pricestreaming/router/SequenceStrategy$$Lambda.0x00007f9eb7c90458.accept',12,0,0)
f(33,13057,1335,1,'io/hydrax/pricestreaming/router/SequenceStrategy.lambda$handle$0',12,0,0)
f(34,13057,1335,1,'io/hydrax/pricestreaming/router/SequenceStrategy.generateOrder',12,0,0)
f(35,13060,13,2,'io/hydrax/pricestreaming/cache/TradingVenueCache.getLpTickerName',9,0,0)
f(36,13060,7,2,'io/hydrax/pricestreaming/cache/TradingVenueCache.get',7,0,0)
f(37,13060,7,2,'java/util/concurrent/ConcurrentHashMap.get',7,0,0)
f(36,13067,6,2,'java/util/stream/ReferencePipeline.findFirst',2,0,0)
f(37,13067,6,2,'java/util/stream/AbstractPipeline.evaluate',2,0,0)
f(38,13067,6,2,'java/util/stream/FindOps$FindOp.evaluateSequential',2,0,0)
f(39,13067,5,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',1,0,0)
f(40,13067,4,1,'java/util/stream/AbstractPipeline.copyInto')
f(41,13067,4,1,'java/util/stream/AbstractPipeline.copyIntoWithCancel')
f(42,13067,3,1,'java/util/stream/ReferencePipeline.forEachWithCancel')
f(43,13067,3,1,'java/util/ArrayList$ArrayListSpliterator.tryAdvance')
f(44,13067,3,1,'java/util/stream/ReferencePipeline$2$1.accept')
f(45,13067,2,1,'io/hydrax/pricestreaming/cache/TradingVenueCache$$Lambda.0x00007f9eb7c90890.test')
f(45,13069,1,3,'itable stub')
f(42,13070,1,1,'java/util/stream/Sink$ChainedReference.end')
f(43,13070,1,3,'itable stub')
f(40,13071,1,2,'java/util/stream/AbstractPipeline.wrapSink',1,0,0)
f(39,13072,1,2,'java/util/stream/FindOps$FindSink$OfRef.get',1,0,0)
f(40,13072,1,2,'java/util/stream/FindOps$FindSink$OfRef.get',1,0,0)
f(41,13072,1,2,'java/util/Optional.of',1,0,0)
f(35,13073,1319,1,'io/hydrax/pricestreaming/service/PostRoutingService.postProcess')
f(36,13094,706,1,'io/hydrax/aeron/client/ClientManager.send')
f(37,13095,2,1,'io/hydrax/pricestreaming/domain/ERResponseList.topic')
f(37,13097,2,1,'io/hydrax/pricestreaming/domain/PlaceOrder.topic')
f(37,13099,3,3,'itable stub')
f(37,13102,698,1,'java/util/stream/ReferencePipeline.forEach',6,0,0)
f(38,13102,697,1,'java/util/stream/AbstractPipeline.evaluate',6,0,0)
f(39,13102,697,1,'java/util/stream/ForEachOps$ForEachOp$OfRef.evaluateSequential',6,0,0)
f(40,13102,697,1,'java/util/stream/ForEachOps$ForEachOp.evaluateSequential',6,0,0)
f(41,13102,697,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',6,0,0)
f(42,13102,689,1,'java/util/stream/AbstractPipeline.copyInto',1,0,0)
f(43,13103,679,1,'java/util/ArrayList$ArrayListSpliterator.forEachRemaining')
f(44,13103,4,3,'itable stub')
f(44,13107,675,1,'java/util/stream/ReferencePipeline$2$1.accept')
f(45,13110,11,1,'io/hydrax/aeron/client/MessageRouter$$Lambda.0x00007f9eb7c17040.test')
f(46,13115,6,2,'io/hydrax/aeron/client/MessageRouter.lambda$route$0',4,0,0)
f(47,13115,6,2,'io/hydrax/aeron/client/MessageRouter$MessageRouteRule.match',4,0,0)
f(48,13116,1,1,'io/hydrax/pricestreaming/domain/ERResponseList.topic')
f(48,13117,1,1,'io/hydrax/pricestreaming/domain/PlaceOrder.topic')
f(48,13118,2,3,'itable stub')
f(48,13120,1,2,'java/lang/String.equals',1,0,0)
f(45,13121,1,3,'itable stub')
f(45,13122,660,1,'java/util/stream/ForEachOps$ForEachOp$OfRef.accept')
f(46,13122,660,1,'io/hydrax/aeron/client/ClientManager$$Lambda.0x00007f9eb7c17288.accept')
f(47,13123,659,1,'io/hydrax/aeron/client/ClientManager.lambda$send$5')
f(48,13123,659,1,'io/hydrax/aeron/client/ClientManager.processSend')
f(49,13123,659,1,'java/util/stream/ReferencePipeline$Head.forEach')
f(50,13123,659,1,'java/util/Spliterators$ArraySpliterator.forEachRemaining')
f(51,13123,659,1,'io/hydrax/aeron/client/ClientManager$$Lambda.0x00007f9eb7c174b0.accept')
f(52,13123,659,1,'io/hydrax/aeron/client/ClientManager.lambda$processSend$7',18,0,0)
f(53,13123,282,1,'io/hydrax/aeron/client/ArchiveClient.send')
f(54,13137,3,2,'io/aeron/Publication.offer',2,0,0)
f(55,13137,3,2,'io/aeron/ConcurrentPublication.offer',3,0,0)
f(56,13139,1,2,'io/aeron/ConcurrentPublication.appendUnfragmentedMessage',1,0,0)
f(57,13139,1,2,'io/aeron/logbuffer/LogBufferDescriptor.termOffset',1,0,0)
f(58,13139,1,2,'java/lang/Math.min',1,0,0)
f(54,13140,1,2,'io/hydrax/aeron/connection/ExponentialBackoffReconnectStrategy.isRunning',1,0,0)
f(55,13140,1,2,'io/hydrax/aeron/connection/ContinuousStrategy.isRunning',1,0,0)
f(56,13140,1,2,'java/util/concurrent/atomic/AtomicBoolean.get',1,0,0)
f(54,13141,225,1,'io/hydrax/pricestreaming/domain/ERResponseList.toByteArray')
f(55,13141,225,1,'com/google/protobuf/AbstractMessageLite.toByteArray')
f(56,13141,72,1,'io/hydrax/proto/metwo/match/ResponseList.getSerializedSize')
f(57,13141,69,1,'com/google/protobuf/CodedOutputStream.computeMessageSize')
f(58,13141,69,1,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag')
f(59,13141,69,1,'io/hydrax/proto/metwo/match/Response.getSerializedSize')
f(60,13142,68,1,'com/google/protobuf/CodedOutputStream.computeMessageSize')
f(61,13142,68,1,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag',4,0,0)
f(62,13142,30,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.getSerializedSize')
f(63,13146,13,2,'com/google/protobuf/CodedOutputStream.computeMessageSize',10,0,0)
f(64,13146,13,2,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag',11,0,0)
f(65,13146,12,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',10,0,0)
f(66,13157,1,1,'com/google/protobuf/UnknownFieldSet.getSerializedSize')
f(65,13158,1,3,'itable stub')
f(63,13159,7,1,'com/google/protobuf/GeneratedMessageV3.computeStringSize')
f(64,13160,6,2,'com/google/protobuf/CodedOutputStream.computeStringSize',6,0,0)
f(65,13160,6,2,'com/google/protobuf/CodedOutputStream.computeStringSizeNoTag',6,0,0)
f(66,13160,6,2,'com/google/protobuf/Utf8.encodedLength',6,0,0)
f(67,13160,6,2,'java/lang/String.charAt',6,0,0)
f(63,13166,5,1,'com/google/protobuf/GeneratedMessageV3.isStringEmpty')
f(63,13171,1,1,'io/hydrax/proto/metwo/match/Side.getNumber')
f(62,13172,34,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.getSerializedSize')
f(63,13176,1,1,'com/google/protobuf/CodedOutputStream.computeEnumSize')
f(63,13177,6,2,'com/google/protobuf/CodedOutputStream.computeMessageSize',5,0,0)
f(64,13177,6,2,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag',6,0,0)
f(65,13177,5,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',5,0,0)
f(65,13182,1,3,'itable stub')
f(63,13183,22,1,'com/google/protobuf/GeneratedMessageV3.computeStringSize')
f(64,13183,22,2,'com/google/protobuf/CodedOutputStream.computeStringSize',22,0,0)
f(65,13183,22,2,'com/google/protobuf/CodedOutputStream.computeStringSizeNoTag',22,0,0)
f(66,13183,22,2,'com/google/protobuf/Utf8.encodedLength',22,0,0)
f(67,13183,22,2,'java/lang/String.charAt',22,0,0)
f(63,13205,1,1,'com/google/protobuf/UnknownFieldSet.getSerializedSize')
f(62,13206,4,3,'itable stub')
f(57,13210,3,2,'java/util/Collections$UnmodifiableList.get',3,0,0)
f(56,13213,153,1,'io/hydrax/proto/metwo/match/ResponseList.writeTo')
f(57,13214,150,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage',5,0,0)
f(58,13214,150,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',5,0,0)
f(59,13218,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(59,13219,145,1,'io/hydrax/proto/metwo/match/Response.writeTo')
f(60,13219,143,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage')
f(61,13220,142,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',2,0,0)
f(62,13220,52,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.writeTo')
f(63,13222,8,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage',4,0,0)
f(64,13222,8,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',4,0,0)
f(65,13222,2,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',2,0,0)
f(65,13224,6,2,'io/hydrax/proto/metwo/match/UDec128.writeTo',2,0,0)
f(66,13225,2,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64',2,0,0)
f(67,13225,2,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64NoTag',2,0,0)
f(66,13227,3,2,'com/google/protobuf/UnknownFieldSet.writeTo',3,0,0)
f(67,13227,3,2,'java/util/TreeMap$EntrySet.iterator',3,0,0)
f(68,13227,3,2,'java/util/TreeMap.getFirstEntry',3,0,0)
f(63,13230,6,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64',6,0,0)
f(64,13230,6,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64NoTag',6,0,0)
f(63,13236,1,1,'com/google/protobuf/GeneratedMessageV3.isStringEmpty')
f(63,13237,34,1,'com/google/protobuf/GeneratedMessageV3.writeString',3,0,0)
f(64,13237,34,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeString',8,0,0)
f(65,13237,33,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeStringNoTag',7,0,0)
f(66,13237,4,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',3,0,0)
f(66,13241,21,1,'com/google/protobuf/Utf8.encode',4,0,0)
f(67,13245,17,1,'com/google/protobuf/Utf8$UnsafeProcessor.encodeUtf8')
f(68,13245,17,2,'java/lang/String.charAt',17,0,0)
f(66,13262,8,1,'com/google/protobuf/Utf8.encodedLength')
f(67,13262,8,2,'java/lang/String.charAt',8,0,0)
f(65,13270,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeTag',1,0,0)
f(66,13270,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',1,0,0)
f(63,13271,1,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport.getPremium')
f(62,13272,88,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport.writeTo')
f(63,13279,22,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage',6,0,0)
f(64,13279,22,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',14,0,0)
f(65,13281,2,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',1,0,0)
f(65,13283,12,2,'io/hydrax/proto/metwo/match/UDec128.writeTo',5,0,0)
f(66,13288,4,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64',4,0,0)
f(67,13288,4,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64NoTag',4,0,0)
f(66,13292,3,2,'com/google/protobuf/UnknownFieldSet.writeTo',3,0,0)
f(67,13292,3,2,'java/util/TreeMap$EntrySet.iterator',3,0,0)
f(68,13292,3,2,'java/util/TreeMap.getFirstEntry',3,0,0)
f(65,13295,6,3,'itable stub')
f(63,13301,2,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64')
f(63,13303,2,1,'com/google/protobuf/CodedOutputStream.writeEnum')
f(64,13303,2,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeInt32',2,0,0)
f(65,13303,2,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeTag',2,0,0)
f(66,13303,2,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag',2,0,0)
f(63,13305,2,1,'com/google/protobuf/GeneratedMessageV3.isStringEmpty')
f(63,13307,50,1,'com/google/protobuf/GeneratedMessageV3.writeString',5,0,0)
f(64,13307,50,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeString',6,0,0)
f(65,13307,50,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeStringNoTag',6,0,0)
f(66,13308,2,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt32NoTag')
f(66,13310,28,1,'com/google/protobuf/Utf8.encode',5,0,0)
f(67,13315,23,1,'com/google/protobuf/Utf8$UnsafeProcessor.encodeUtf8')
f(68,13315,23,2,'java/lang/String.charAt',23,0,0)
f(66,13338,19,1,'com/google/protobuf/Utf8.encodedLength')
f(67,13338,19,2,'java/lang/String.charAt',19,0,0)
f(63,13357,2,1,'io/hydrax/proto/metwo/match/PsExecType.getNumber')
f(63,13359,1,1,'io/hydrax/proto/metwo/match/PsOrderType.getNumber')
f(62,13360,2,3,'itable stub')
f(60,13362,2,2,'com/google/protobuf/UnknownFieldSet.writeTo',2,0,0)
f(61,13362,2,2,'java/util/TreeMap$EntrySet.iterator',2,0,0)
f(62,13362,2,2,'java/util/TreeMap.getFirstEntry',2,0,0)
f(57,13364,1,2,'com/google/protobuf/GeneratedMessageV3.writeString',1,0,0)
f(58,13364,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeString',1,0,0)
f(59,13364,1,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeStringNoTag',1,0,0)
f(60,13364,1,2,'com/google/protobuf/Utf8.encode',1,0,0)
f(57,13365,1,1,'java/util/Collections$UnmodifiableCollection.size')
f(58,13365,1,1,'java/util/ArrayList.size')
f(54,13366,1,2,'io/netty/util/concurrent/FastThreadLocal.get',1,0,0)
f(55,13366,1,2,'io/netty/util/internal/InternalThreadLocalMap.get',1,0,0)
f(54,13367,8,2,'io/netty/util/concurrent/FastThreadLocal.isSet',8,0,0)
f(55,13367,8,2,'io/netty/util/internal/InternalThreadLocalMap.getIfSet',8,0,0)
f(56,13371,1,2,'java/lang/ThreadLocal.get',1,0,0)
f(57,13371,1,2,'java/lang/ThreadLocal.get',1,0,0)
f(58,13371,1,2,'java/lang/ThreadLocal.setInitialValue',1,0,0)
f(59,13371,1,2,'java/lang/ThreadLocal$ThreadLocalMap.set',1,0,0)
f(60,13371,1,2,'java/lang/ThreadLocal$ThreadLocalMap$Entry.<init>',1,0,0)
f(56,13372,2,3,'jbyte_disjoint_arraycopy_avx3')
f(56,13374,1,3,'jlong_disjoint_arraycopy_avx3')
f(54,13375,30,1,'io/netty/util/concurrent/FastThreadLocal.set')
f(55,13375,10,1,'io/netty/util/concurrent/FastThreadLocal.setKnownNotUnset')
f(56,13375,10,1,'io/netty/util/concurrent/FastThreadLocal.addToVariablesToRemove')
f(57,13375,8,1,'java/util/Collections$SetFromMap.add')
f(58,13375,1,1,'java/util/IdentityHashMap.put')
f(58,13376,7,1,'java/util/WeakHashMap.put')
f(59,13376,7,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(60,13376,7,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(61,13376,7,4,'MemAllocator::allocate() const')
f(62,13380,2,4,'MutableSpace::cas_allocate(unsigned long)')
f(62,13382,1,3,'__memset_avx512_unaligned_erms')
f(57,13383,2,1,'java/util/Collections.newSetFromMap')
f(58,13383,2,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(59,13383,2,4,'InstanceKlass::allocate_objArray(int, int, JavaThread*)')
f(60,13383,2,4,'MemAllocator::allocate() const')
f(61,13383,2,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(62,13383,2,4,'ThreadLocalAllocBuffer::retire_before_allocation()')
f(63,13383,2,4,'CollectedHeap::fill_with_dummy_object(HeapWordImpl**, HeapWordImpl**, bool)')
f(55,13385,20,1,'io/netty/util/internal/InternalThreadLocalMap.get')
f(56,13385,20,1,'io/netty/util/internal/InternalThreadLocalMap.slowGet')
f(57,13396,2,1,'io/netty/util/internal/InternalThreadLocalMap.<init>')
f(58,13396,2,1,'io/netty/util/internal/InternalThreadLocalMap.newIndexedVariableTable')
f(59,13396,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<598116ul, CardTableBarrierSet>, (AccessInternal::BarrierType)2, 598116ul>::oop_access_barrier(void*)')
f(59,13397,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(60,13397,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(61,13397,1,4,'MemAllocator::allocate() const')
f(57,13398,4,2,'java/lang/ThreadLocal.get',4,0,0)
f(58,13398,4,2,'java/lang/ThreadLocal.get',4,0,0)
f(59,13398,4,2,'java/lang/ThreadLocal$ThreadLocalMap.getEntry',4,0,0)
f(60,13398,4,2,'java/lang/ThreadLocal$ThreadLocalMap.getEntryAfterMiss',4,0,0)
f(57,13402,3,1,'java/lang/ThreadLocal.set')
f(58,13402,3,2,'java/lang/ThreadLocal.set',3,0,0)
f(59,13402,3,2,'java/lang/ThreadLocal$ThreadLocalMap.set',3,0,0)
f(53,13405,13,2,'io/hydrax/aeron/client/ClientManager.getClient',13,0,0)
f(54,13405,13,2,'java/util/HashMap.get',13,0,0)
f(55,13405,13,2,'java/util/HashMap.getNode',13,0,0)
f(53,13418,342,1,'io/hydrax/aeron/client/ClusterClient.send')
f(54,13455,72,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(55,13456,71,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(56,13456,1,4,'Klass::check_array_allocation_length(int, int, JavaThread*)')
f(56,13457,70,4,'MemAllocator::allocate() const')
f(57,13463,1,4,'AllocTracer::send_allocation_in_new_tlab(Klass*, HeapWordImpl**, unsigned long, unsigned long, JavaThread*)')
f(58,13463,1,4,'JfrObjectAllocationSample::send_event(Klass const*, unsigned long, bool, Thread*)')
f(57,13464,1,4,'AllocTracer::send_allocation_outside_tlab(Klass*, HeapWordImpl**, unsigned long, JavaThread*)')
f(58,13464,1,4,'ObjectSampler::is_created()')
f(57,13465,3,4,'MutableSpace::cas_allocate(unsigned long)')
f(57,13468,6,4,'ObjArrayAllocator::initialize(HeapWordImpl**) const')
f(57,13474,1,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(57,13475,52,3,'__memset_avx512_unaligned_erms')
f(54,13527,4,4,'SharedRuntime::on_slowpath_allocation_exit(JavaThread*)')
f(54,13531,1,4,'SharedRuntime::resolve_virtual_call_C(JavaThread*)')
f(55,13531,1,4,'SharedRuntime::resolve_helper(bool, bool, JavaThread*)')
f(56,13531,1,4,'SharedRuntime::resolve_sub_helper(bool, bool, JavaThread*)')
f(54,13532,19,1,'io/aeron/cluster/client/AeronCluster.offer',2,0,0)
f(55,13532,19,2,'io/aeron/ConcurrentPublication.offer',13,0,0)
f(56,13532,19,2,'io/aeron/ConcurrentPublication.appendUnfragmentedMessage',19,0,0)
f(57,13532,19,2,'org/agrona/AbstractMutableDirectBuffer.putBytes',19,0,0)
f(58,13532,1,3,'jlong_disjoint_arraycopy_avx3')
f(58,13533,18,2,'sun/misc/Unsafe.copyMemory',18,0,0)
f(59,13533,18,2,'jdk/internal/misc/Unsafe.copyMemory',18,0,0)
f(60,13533,18,2,'jdk/internal/misc/Unsafe.copyMemoryChecks',18,0,0)
f(61,13533,18,2,'jdk/internal/misc/Unsafe.checkPrimitivePointer',18,0,0)
f(62,13533,18,2,'jdk/internal/misc/Unsafe.checkPointer',18,0,0)
f(63,13533,18,2,'jdk/internal/misc/Unsafe.checkOffset',18,0,0)
f(64,13550,1,3,'jlong_disjoint_arraycopy_avx3')
f(54,13551,138,1,'io/hydrax/pricestreaming/domain/PlaceOrder.toByteArray',13,0,0)
f(55,13551,138,1,'com/google/protobuf/AbstractMessageLite.toByteArray',13,0,0)
f(56,13564,49,1,'io/hydrax/proto/metwo/match/Request.getSerializedSize')
f(57,13564,49,1,'com/google/protobuf/CodedOutputStream.computeMessageSize',3,0,0)
f(58,13564,49,1,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag',3,0,0)
f(59,13567,46,1,'io/hydrax/proto/metwo/match/PsOrder.getSerializedSize')
f(60,13571,33,2,'com/google/protobuf/CodedOutputStream.computeMessageSize',23,0,0)
f(61,13573,29,2,'com/google/protobuf/CodedOutputStream.computeMessageSizeNoTag',26,0,0)
f(62,13574,26,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',23,0,0)
f(62,13600,2,3,'itable stub')
f(61,13602,2,2,'com/google/protobuf/CodedOutputStream.computeTagSize',2,0,0)
f(62,13602,2,2,'com/google/protobuf/CodedOutputStream.computeUInt32SizeNoTag',2,0,0)
f(60,13604,4,1,'com/google/protobuf/GeneratedMessageV3.computeStringSize')
f(61,13604,4,2,'com/google/protobuf/CodedOutputStream.computeStringSize',4,0,0)
f(62,13604,4,2,'com/google/protobuf/CodedOutputStream.computeStringSizeNoTag',4,0,0)
f(63,13604,4,2,'com/google/protobuf/Utf8.encodedLength',4,0,0)
f(64,13604,4,2,'java/lang/String.charAt',4,0,0)
f(60,13608,1,1,'com/google/protobuf/UnknownFieldSet.getSerializedSize')
f(60,13609,2,1,'io/hydrax/proto/metwo/match/CancelReason.getNumber')
f(60,13611,2,1,'io/hydrax/proto/metwo/match/PsOrder.getEarmarkTaxAmt')
f(56,13613,76,1,'io/hydrax/proto/metwo/match/Request.writeTo')
f(57,13613,72,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage',2,0,0)
f(58,13613,72,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',2,0,0)
f(59,13615,70,1,'io/hydrax/proto/metwo/match/PsOrder.writeTo')
f(60,13617,12,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessage',8,0,0)
f(61,13617,12,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeMessageNoTag',11,0,0)
f(62,13617,1,2,'io/hydrax/proto/metwo/match/UDec128.getSerializedSize',1,0,0)
f(62,13618,8,2,'io/hydrax/proto/metwo/match/UDec128.writeTo',7,0,0)
f(63,13619,6,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64',6,0,0)
f(64,13619,6,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64NoTag',6,0,0)
f(63,13625,1,2,'com/google/protobuf/UnknownFieldSet.writeTo',1,0,0)
f(64,13625,1,2,'java/util/TreeMap$EntrySet.iterator',1,0,0)
f(65,13625,1,2,'java/util/TreeMap.getFirstEntry',1,0,0)
f(62,13626,3,3,'itable stub')
f(60,13629,4,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64',3,0,0)
f(61,13630,3,2,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeUInt64NoTag',3,0,0)
f(60,13633,2,1,'com/google/protobuf/GeneratedMessageV3.isStringEmpty')
f(60,13635,48,1,'com/google/protobuf/GeneratedMessageV3.writeString',7,0,0)
f(61,13635,48,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeString',11,0,0)
f(62,13635,48,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeStringNoTag',11,0,0)
f(63,13638,30,1,'com/google/protobuf/Utf8.encode',8,0,0)
f(64,13646,22,1,'com/google/protobuf/Utf8$UnsafeProcessor.encodeUtf8')
f(65,13647,21,2,'java/lang/String.charAt',21,0,0)
f(63,13668,15,1,'com/google/protobuf/Utf8.encodedLength')
f(64,13668,15,2,'java/lang/String.charAt',15,0,0)
f(60,13683,1,1,'com/google/protobuf/UnknownFieldSet.writeTo')
f(61,13683,1,2,'java/util/TreeMap$EntrySet.iterator',1,0,0)
f(62,13683,1,2,'java/util/TreeMap.getFirstEntry',1,0,0)
f(60,13684,1,1,'io/hydrax/proto/metwo/match/PsOrder.getEarmarkTaxAmt')
f(57,13685,4,1,'com/google/protobuf/GeneratedMessageV3.writeString')
f(58,13685,4,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeString')
f(59,13685,4,1,'com/google/protobuf/CodedOutputStream$ArrayEncoder.writeStringNoTag')
f(60,13685,4,1,'com/google/protobuf/Utf8.encode')
f(61,13685,4,1,'com/google/protobuf/Utf8$UnsafeProcessor.encodeUtf8')
f(62,13685,4,2,'java/lang/String.charAt',4,0,0)
f(54,13689,1,1,'io/netty/util/concurrent/FastThreadLocal.get')
f(55,13689,1,1,'io/netty/util/internal/InternalThreadLocalMap.get')
f(56,13689,1,1,'io/netty/util/internal/InternalThreadLocalMap.slowGet')
f(57,13689,1,2,'java/lang/ThreadLocal.get',1,0,0)
f(58,13689,1,2,'java/lang/ThreadLocal.get',1,0,0)
f(59,13689,1,2,'java/lang/ThreadLocal.setInitialValue',1,0,0)
f(60,13689,1,2,'java/lang/ThreadLocal$ThreadLocalMap.set',1,0,0)
f(54,13690,8,2,'io/netty/util/concurrent/FastThreadLocal.isSet',8,0,0)
f(55,13690,8,2,'io/netty/util/internal/InternalThreadLocalMap.getIfSet',8,0,0)
f(56,13690,7,2,'java/lang/ThreadLocal.get',7,0,0)
f(57,13690,7,2,'java/lang/ThreadLocal.get',7,0,0)
f(58,13690,7,2,'java/lang/ThreadLocal$ThreadLocalMap.getEntry',7,0,0)
f(59,13690,7,2,'java/lang/ThreadLocal$ThreadLocalMap.getEntryAfterMiss',7,0,0)
f(56,13697,1,3,'jlong_disjoint_arraycopy_avx3')
f(54,13698,4,1,'io/netty/util/concurrent/FastThreadLocal.set',1,0,0)
f(55,13698,3,2,'io/netty/util/concurrent/FastThreadLocal.setKnownNotUnset',1,0,0)
f(56,13698,1,4,'SharedRuntime::resolve_static_call_C(JavaThread*)')
f(57,13698,1,4,'SharedRuntime::resolve_helper(bool, bool, JavaThread*)')
f(58,13698,1,4,'SharedRuntime::resolve_sub_helper(bool, bool, JavaThread*)')
f(59,13698,1,4,'SharedRuntime::find_callee_info(Bytecodes::Code&, CallInfo&, JavaThread*)')
f(60,13698,1,4,'vframeStream::vframeStream(JavaThread*, bool, bool, bool)')
f(56,13699,2,2,'io/netty/util/concurrent/FastThreadLocal.addToVariablesToRemove',1,0,0)
f(57,13699,1,2,'io/netty/util/internal/InternalThreadLocalMap.indexedVariable',1,0,0)
f(57,13700,1,1,'java/util/Collections$SetFromMap.add')
f(58,13700,1,1,'java/util/IdentityHashMap.put')
f(55,13701,1,1,'io/netty/util/internal/InternalThreadLocalMap.get')
f(56,13701,1,1,'io/netty/util/internal/InternalThreadLocalMap.slowGet')
f(57,13701,1,2,'java/lang/ThreadLocal.get',1,0,0)
f(58,13701,1,2,'java/lang/ThreadLocal.get',1,0,0)
f(59,13701,1,2,'java/lang/ThreadLocal.setInitialValue',1,0,0)
f(60,13701,1,2,'java/lang/ThreadLocal$ThreadLocalMap.set',1,0,0)
f(54,13702,55,1,'java/lang/Long.valueOf')
f(55,13702,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(55,13703,52,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(56,13706,49,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(57,13706,47,4,'MemAllocator::allocate() const')
f(58,13712,1,4,'AllocTracer::send_allocation_outside_tlab(Klass*, HeapWordImpl**, unsigned long, JavaThread*)')
f(58,13713,1,4,'MemAllocator::Allocation::check_out_of_memory()')
f(58,13714,1,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(58,13715,2,4,'MutableSpace::cas_allocate(unsigned long)')
f(58,13717,1,4,'ParallelScavengeHeap::mem_allocate(unsigned long, bool*)')
f(58,13718,1,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(58,13719,34,3,'__memset_avx512_unaligned_erms')
f(57,13753,2,4,'ParallelScavengeHeap::mem_allocate(unsigned long, bool*)')
f(55,13755,2,4,'SharedRuntime::on_slowpath_allocation_exit(JavaThread*)')
f(54,13757,1,2,'org/agrona/AbstractMutableDirectBuffer.putBytes',1,0,0)
f(55,13757,1,2,'sun/misc/Unsafe.copyMemory',1,0,0)
f(56,13757,1,2,'jdk/internal/misc/Unsafe.copyMemory',1,0,0)
f(57,13757,1,2,'jdk/internal/misc/Unsafe.copyMemoryChecks',1,0,0)
f(58,13757,1,2,'jdk/internal/misc/Unsafe.checkPrimitivePointer',1,0,0)
f(59,13757,1,2,'jdk/internal/misc/Unsafe.checkPointer',1,0,0)
f(60,13757,1,2,'jdk/internal/misc/Unsafe.checkOffset',1,0,0)
f(54,13758,1,2,'org/agrona/AbstractMutableDirectBuffer.putLong',1,0,0)
f(55,13758,1,2,'org/agrona/concurrent/UnsafeBuffer.ensureCapacity',1,0,0)
f(56,13758,1,2,'org/agrona/AbstractMutableDirectBuffer.boundsCheck0',1,0,0)
f(54,13759,1,1,'org/agrona/concurrent/UnsafeBuffer.<init>')
f(55,13759,1,4,'Runtime1::new_type_array(JavaThread*, Klass*, int)')
f(56,13759,1,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(57,13759,1,4,'MemAllocator::allocate() const')
f(58,13759,1,3,'__memset_avx512_unaligned_erms')
f(53,13760,18,1,'io/hydrax/pricestreaming/aeron/config/AeronConfiguration$$Lambda.0x00007f9eb77836f0.get',1,0,0)
f(54,13760,18,1,'io/hydrax/pricestreaming/aeron/config/AeronConfiguration.lambda$sequenceProvider$1',1,0,0)
f(55,13760,18,1,'io/hydrax/pricestreaming/cache/SequenceCache.getAndIncrement',1,0,0)
f(56,13761,17,1,'java/util/concurrent/ConcurrentHashMap.computeIfAbsent')
f(57,13774,2,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(58,13774,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<594020ul, CardTableBarrierSet>, (AccessInternal::BarrierType)2, 594020ul>::oop_access_barrier(void*)')
f(58,13775,1,4,'ObjectSynchronizer::enter(Handle, BasicLock*, JavaThread*)')
f(59,13775,1,4,'ObjectMonitor::enter(JavaThread*)')
f(60,13775,1,4,'ObjectMonitor::TrySpin(JavaThread*) [clone .part.0]')
f(57,13776,1,1,'java/lang/String.hashCode')
f(57,13777,1,3,'vtable stub')
f(53,13778,4,2,'java/util/HashMap.get',4,0,0)
f(54,13778,4,2,'java/util/HashMap.getNode',4,0,0)
f(43,13782,2,1,'java/util/Spliterator.getExactSizeIfKnown')
f(44,13782,2,1,'java/util/ArrayList$ArrayListSpliterator.characteristics')
f(43,13784,6,1,'java/util/stream/ReferencePipeline$2$1.begin')
f(44,13785,2,3,'itable stub')
f(44,13787,3,1,'java/util/stream/Sink.begin')
f(43,13790,1,1,'java/util/stream/Sink$ChainedReference.end')
f(44,13790,1,3,'itable stub')
f(42,13791,8,2,'java/util/stream/AbstractPipeline.wrapSink',5,0,0)
f(43,13796,3,1,'java/util/stream/ReferencePipeline$2.opWrapSink')
f(44,13796,3,2,'java/util/stream/ReferencePipeline$2$1.<init>',3,0,0)
f(45,13796,3,2,'java/util/stream/Sink$ChainedReference.<init>',3,0,0)
f(38,13799,1,1,'java/util/stream/ForEachOps.makeRef')
f(39,13799,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(40,13799,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(41,13799,1,4,'MemAllocator::allocate() const')
f(36,13800,2,2,'io/hydrax/pricestreaming/cache/MarketCache.getMarketModelBySymbolCode',2,0,0)
f(37,13800,2,2,'java/util/concurrent/ConcurrentHashMap.get',2,0,0)
f(36,13802,6,1,'io/hydrax/pricestreaming/cache/OrderCache.put')
f(37,13802,6,1,'it/unimi/dsi/fastutil/objects/Object2LongFunctions$SynchronizedFunction.put')
f(38,13802,6,1,'it/unimi/dsi/fastutil/objects/Object2LongFunctions$SynchronizedFunction.put')
f(39,13802,4,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(40,13802,3,4,'ObjectSynchronizer::enter(Handle, BasicLock*, JavaThread*)')
f(41,13802,1,4,'ObjectMonitor::enter(JavaThread*)')
f(42,13802,1,3,'_SafeFetch32_fault')
f(41,13803,2,4,'ObjectSynchronizer::inflate(Thread*, oopDesc*, ObjectSynchronizer::InflateCause)')
f(40,13805,1,4,'ObjectSynchronizer::quick_enter(oopDesc*, JavaThread*, BasicLock*)')
f(39,13806,1,3,'_pthread_cleanup_push')
f(39,13807,1,1,'it/unimi/dsi/fastutil/objects/Object2LongMap.put')
f(40,13807,1,1,'it/unimi/dsi/fastutil/objects/Object2LongFunction.put')
f(41,13807,1,1,'it/unimi/dsi/fastutil/objects/Object2LongOpenHashMap.containsKey')
f(42,13807,1,1,'java/lang/String.equals')
f(36,13808,16,1,'io/hydrax/pricestreaming/cache/OrderCache.putChildOrder')
f(37,13808,16,1,'it/unimi/dsi/fastutil/objects/Object2ObjectFunctions$SynchronizedFunction.put')
f(38,13808,1,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(39,13808,1,4,'ObjectSynchronizer::enter(Handle, BasicLock*, JavaThread*)')
f(40,13808,1,4,'ObjectMonitor::enter(JavaThread*)')
f(41,13808,1,4,'ObjectMonitor::TrySpin(JavaThread*) [clone .part.0]')
f(38,13809,15,2,'it/unimi/dsi/fastutil/objects/Object2ObjectOpenHashMap.put',15,0,0)
f(39,13809,15,2,'it/unimi/dsi/fastutil/objects/Object2ObjectOpenHashMap.find',15,0,0)
f(36,13824,12,1,'io/hydrax/pricestreaming/cache/OrderCache.putChildOrderList')
f(37,13824,12,2,'it/unimi/dsi/fastutil/longs/Long2ObjectFunctions$SynchronizedFunction.put',10,0,0)
f(38,13824,12,2,'it/unimi/dsi/fastutil/longs/Long2ObjectFunctions$SynchronizedFunction.put',10,0,0)
f(39,13824,2,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(40,13824,1,4,'ObjectSynchronizer::enter(Handle, BasicLock*, JavaThread*)')
f(41,13824,1,4,'ObjectMonitor::enter(JavaThread*)')
f(42,13824,1,4,'ObjectMonitor::TrySpin(JavaThread*) [clone .part.0]')
f(40,13825,1,4,'ObjectSynchronizer::quick_enter(oopDesc*, JavaThread*, BasicLock*)')
f(39,13826,10,2,'it/unimi/dsi/fastutil/longs/Long2ObjectMap.put',10,0,0)
f(40,13826,10,2,'it/unimi/dsi/fastutil/longs/Long2ObjectFunction.put',10,0,0)
f(41,13826,10,2,'it/unimi/dsi/fastutil/longs/Long2ObjectOpenHashMap.containsKey',10,0,0)
f(36,13836,4,1,'io/hydrax/pricestreaming/config/MarketModelServiceFactory.get')
f(37,13836,4,2,'java/util/HashMap.get',2,0,0)
f(38,13836,4,2,'java/util/HashMap.getNode',2,0,0)
f(39,13838,2,1,'java/lang/String.equals')
f(36,13840,1,1,'io/hydrax/pricestreaming/domain/PlaceOrder.setRequest')
f(36,13841,103,1,'io/hydrax/pricestreaming/service/OrderService_ClientProxy.generateParentOrder',33,0,0)
f(37,13841,102,1,'io/hydrax/pricestreaming/service/OrderService.generateParentOrder',32,0,0)
f(38,13841,4,1,'io/hydrax/pricestreaming/cache/OrderCache.put')
f(39,13841,4,1,'it/unimi/dsi/fastutil/longs/Long2ObjectFunctions$SynchronizedFunction.put')
f(40,13841,4,1,'it/unimi/dsi/fastutil/longs/Long2ObjectFunctions$SynchronizedFunction.put')
f(41,13841,4,4,'SharedRuntime::complete_monitor_locking_C(oopDesc*, BasicLock*, JavaThread*)')
f(42,13841,1,4,'ObjectMonitor::object_peek() const')
f(42,13842,3,4,'ObjectSynchronizer::enter(Handle, BasicLock*, JavaThread*)')
f(43,13842,3,4,'ObjectMonitor::enter(JavaThread*)')
f(44,13842,1,4,'ObjectMonitor::EnterI(JavaThread*)')
f(45,13842,1,3,'_SafeFetch32_fault')
f(44,13843,1,4,'ObjectMonitor::TrySpin(JavaThread*) [clone .part.0]')
f(44,13844,1,3,'_SafeFetch32_fault')
f(38,13845,70,1,'io/hydrax/pricestreaming/cache/OrderCache.putParentOrderRemainingEarmark',20,0,0)
f(39,13865,43,1,'java/util/concurrent/ConcurrentHashMap.computeIfAbsent')
f(40,13878,1,1,'io/hydrax/pricestreaming/cache/OrderCache$$Lambda.0x00007f9eb7c90ec8.apply')
f(40,13879,29,1,'java/util/concurrent/ConcurrentHashMap.addCount')
f(41,13884,24,1,'java/util/concurrent/ConcurrentHashMap.transfer',0,0,3)
f(42,13906,1,2,'java/util/concurrent/ConcurrentHashMap.resizeStamp',1,0,0)
f(42,13907,1,2,'java/util/concurrent/ConcurrentHashMap.tabAt',1,0,0)
f(39,13908,7,1,'java/util/concurrent/ConcurrentHashMap.put')
f(40,13908,7,1,'java/util/concurrent/ConcurrentHashMap.putVal')
f(41,13913,1,2,'java/util/concurrent/ConcurrentHashMap.addCount',1,0,0)
f(41,13914,1,1,'java/util/concurrent/ConcurrentHashMap.initTable')
f(38,13915,28,2,'io/hydrax/pricestreaming/service/OrderService.buildOrderMessage',12,0,0)
f(39,13915,28,2,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.build',12,0,0)
f(40,13925,1,3,'clock_gettime@plt')
f(40,13926,16,1,'io/hydrax/proto/metwo/match/PsParentOrderExecReport$Builder.buildPartial')
f(40,13942,1,4,'os::javaTimeMillis()')
f(37,13943,1,2,'io/hydrax/pricestreaming/service/OrderService_ClientProxy.arc$delegate',1,0,0)
f(38,13943,1,2,'io/quarkus/arc/impl/ClientProxies.getApplicationScopedDelegate',1,0,0)
f(39,13943,1,2,'io/quarkus/arc/impl/AbstractSharedContext.get',1,0,0)
f(40,13943,1,2,'io/quarkus/arc/impl/Scopes.scopeMatches',1,0,0)
f(36,13944,8,1,'io/hydrax/pricestreaming/service/TradingVenueAccountService.getTradingVenueAccount')
f(37,13944,8,1,'io/hydrax/pricestreaming/service/impl/BrokerService.getTradingVenueAccount')
f(38,13944,8,2,'io/hydrax/pricestreaming/cache/TradingVenueAccountCache.get',8,0,0)
f(39,13944,8,2,'java/util/concurrent/ConcurrentHashMap.get',8,0,0)
f(36,13952,42,1,'io/hydrax/pricestreaming/service/impl/BrokerService.buildChildOrder')
f(37,13952,42,2,'io/hydrax/pricestreaming/service/MarketModelService.buildChildOrder',18,0,0)
f(38,13952,34,2,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$Builder.build',18,0,0)
f(39,13970,16,1,'io/hydrax/proto/metwo/match/PsChildOrderExecReport$Builder.buildPartial')
f(40,13985,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(41,13985,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(42,13985,1,4,'MemAllocator::allocate() const')
f(43,13985,1,4,'ObjAllocator::initialize(HeapWordImpl**) const')
f(38,13986,8,1,'io/hydrax/proto/metwo/match/ResponseList$Builder.addResponses')
f(39,13986,1,1,'io/hydrax/proto/metwo/match/ResponseList$Builder.ensureResponsesIsMutable')
f(40,13986,1,1,'java/util/ArrayList.<init>')
f(41,13986,1,1,'java/util/Collections$EmptyList.toArray')
f(39,13987,7,2,'java/util/ArrayList.add',3,0,0)
f(40,13987,7,2,'java/util/ArrayList.add',3,0,0)
f(41,13987,7,2,'java/util/ArrayList.grow',3,0,0)
f(42,13987,7,2,'java/util/ArrayList.grow',3,0,0)
f(43,13990,4,1,'java/util/Arrays.copyOf')
f(36,13994,9,1,'io/hydrax/pricestreaming/utils/TopicUtil.create')
f(37,13994,6,1,'java/lang/String.join')
f(38,13994,5,2,'java/lang/String.join',5,0,0)
f(38,13999,1,1,'java/lang/String.valueOf')
f(39,13999,1,1,'java/lang/String.toString')
f(37,14000,3,2,'java/lang/invoke/Invokers$Holder.linkToTargetMethod',3,0,0)
f(38,14000,3,2,'java/lang/invoke/LambdaForm$MH.0x00007f9eb71a1000.invoke',3,0,0)
f(39,14000,3,2,'java/lang/invoke/DirectMethodHandle$Holder.invokeStatic',3,0,0)
f(40,14000,3,2,'java/lang/StringConcatHelper.newArray',3,0,0)
f(41,14000,3,2,'jdk/internal/misc/Unsafe.allocateUninitializedArray',3,0,0)
f(36,14003,10,1,'io/hydrax/pricestreaming/utils/UDec128Util.from')
f(37,14003,10,1,'io/hydrax/pricestreaming/utils/UDec128Util.fromBigDecimalToUDec128')
f(38,14003,2,1,'java/math/BigDecimal.multiply')
f(39,14003,2,2,'java/math/BigDecimal.multiply',2,0,0)
f(38,14005,8,1,'java/math/BigDecimal.toBigInteger')
f(39,14005,8,1,'java/math/BigDecimal.setScale')
f(40,14008,4,2,'java/math/BigDecimal.divideAndRound',2,0,0)
f(41,14008,4,2,'java/math/MutableBigInteger.divide',2,0,0)
f(42,14010,2,1,'java/math/MutableBigInteger.divideOneWord')
f(40,14012,1,3,'jint_disjoint_arraycopy_avx3')
f(36,14013,317,1,'io/hydrax/pricestreaming/utils/UDec128Util.toBigDecimal')
f(37,14014,15,1,'java/lang/Long.toUnsignedString',1,0,0)
f(38,14014,15,1,'java/lang/Long.toUnsignedString',1,0,0)
f(39,14014,15,1,'java/lang/Long.toString',1,0,0)
f(40,14015,14,1,'java/lang/Long.toString')
f(37,14029,273,1,'java/math/BigDecimal.divide',5,0,0)
f(38,14029,269,1,'java/math/BigDecimal.divide',4,0,0)
f(39,14031,2,2,'java/math/BigDecimal.bigMultiplyPowerTen',2,0,0)
f(40,14031,2,2,'java/math/BigDecimal.bigTenToThe',2,0,0)
f(39,14033,265,1,'java/math/BigDecimal.divideAndRound')
f(40,14034,244,1,'java/math/BigDecimal.createAndStripZerosToMatchScale',21,0,0)
f(41,14047,6,2,'java/math/BigDecimal.valueOf',6,0,0)
f(41,14053,223,1,'java/math/BigInteger.divideAndRemainder')
f(42,14055,1,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(43,14055,1,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(42,14056,220,2,'java/math/BigInteger.divideAndRemainderKnuth',101,0,0)
f(43,14104,3,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(44,14105,1,4,'InstanceKlass::allocate_objArray(int, int, JavaThread*)')
f(45,14105,1,4,'MemAllocator::allocate() const')
f(46,14105,1,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(47,14105,1,4,'ThreadLocalAllocBuffer::fill(HeapWordImpl**, HeapWordImpl**, unsigned long)')
f(44,14106,1,4,'TypeArrayKlass::allocate_common(int, bool, JavaThread*)')
f(45,14106,1,4,'MemAllocator::allocate() const')
f(43,14107,1,4,'SharedRuntime::on_slowpath_allocation_exit(JavaThread*)')
f(43,14108,1,3,'arrayof_jint_fill')
f(43,14109,3,1,'java/math/MutableBigInteger.<init>')
f(44,14109,3,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(45,14109,2,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(46,14109,1,4,'MemAllocator::allocate() const')
f(47,14109,1,4,'ParallelScavengeHeap::mem_allocate(unsigned long, bool*)')
f(48,14109,1,4,'Mutex::lock()')
f(49,14109,1,3,'__lll_lock_wait')
f(46,14110,1,3,'__tls_get_addr')
f(45,14111,1,4,'PSCardTable::is_in_young(void const*) const')
f(43,14112,153,1,'java/math/MutableBigInteger.divideKnuth',48,0,0)
f(44,14112,153,1,'java/math/MutableBigInteger.divideKnuth',48,0,0)
f(45,14160,1,1,'java/math/MutableBigInteger.<init>')
f(46,14160,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(47,14160,1,4,'PSCardTable::is_in_young(void const*) const')
f(45,14161,104,1,'java/math/MutableBigInteger.divideOneWord')
f(46,14263,2,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(43,14265,5,1,'java/math/MutableBigInteger.toBigInteger')
f(44,14265,1,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(44,14266,3,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(45,14267,2,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(46,14267,2,4,'MemAllocator::allocate() const')
f(47,14267,1,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(47,14268,1,4,'ThreadLocalAllocBuffer::fill(HeapWordImpl**, HeapWordImpl**, unsigned long)')
f(44,14269,1,1,'java/math/MutableBigInteger.getMagnitudeArray')
f(45,14269,1,1,'java/util/Arrays.copyOfRange')
f(46,14269,1,1,'java/util/Arrays.copyOfRangeInt')
f(47,14269,1,4,'OptoRuntime::new_array_nozero_C(Klass*, int, JavaThread*)')
f(48,14269,1,4,'OptoRuntime::is_deoptimized_caller_frame(JavaThread*)')
f(49,14269,1,4,'CodeCache::find_blob(void*)')
f(43,14270,5,3,'jint_disjoint_arraycopy_avx3')
f(43,14275,1,4,'oopFactory::new_objArray(Klass*, int, JavaThread*)')
f(41,14276,1,3,'jint_conjoint_arraycopy_avx3')
f(41,14277,1,3,'jint_disjoint_arraycopy_avx3')
f(40,14278,20,1,'java/math/MutableBigInteger.divide',1,0,0)
f(41,14279,19,1,'java/math/MutableBigInteger.divideLongMagnitude')
f(42,14294,2,2,'java/math/MutableBigInteger.divWord',2,0,0)
f(42,14296,1,3,'jint_conjoint_arraycopy_avx3')
f(42,14297,1,3,'jint_disjoint_arraycopy_avx3')
f(38,14298,4,1,'java/math/BigDecimal.precision',1,0,0)
f(39,14299,3,1,'java/math/BigDecimal.longDigitLength')
f(37,14302,26,1,'java/math/BigInteger.<init>')
f(38,14302,26,1,'java/math/BigInteger.<init>')
f(39,14317,4,2,'java/lang/Integer.parseInt',3,0,0)
f(40,14317,4,2,'java/lang/String.charAt',3,0,0)
f(41,14320,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(39,14321,7,2,'java/lang/String.lastIndexOf',7,0,0)
f(40,14321,7,2,'java/lang/String.lastIndexOf',7,0,0)
f(41,14321,7,2,'java/lang/StringLatin1.lastIndexOf',7,0,0)
f(37,14328,2,1,'java/math/BigInteger.or')
f(38,14328,2,2,'java/math/BigInteger.getInt',2,0,0)
f(36,14330,19,1,'io/hydrax/proto/metwo/match/PsOrder$Builder.build')
f(37,14330,19,1,'io/hydrax/proto/metwo/match/PsOrder$Builder.buildPartial')
f(38,14348,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(39,14348,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(40,14348,1,4,'MemAllocator::allocate() const')
f(41,14348,1,4,'AllocTracer::send_allocation_in_new_tlab(Klass*, HeapWordImpl**, unsigned long, unsigned long, JavaThread*)')
f(42,14348,1,4,'JfrObjectAllocationSample::send_event(Klass const*, unsigned long, bool, Thread*)')
f(36,14349,14,1,'io/hydrax/proto/metwo/match/PsOrder$Builder.mergeFrom')
f(37,14350,13,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',13,0,0)
f(36,14363,1,1,'io/hydrax/proto/metwo/match/PsOrder$Builder.setClOrdId')
f(37,14363,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(36,14364,1,1,'io/hydrax/proto/metwo/match/PsOrder$Builder.setFixRequestId')
f(37,14364,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(36,14365,1,1,'io/hydrax/proto/metwo/match/PsOrder$Builder.setPrice')
f(37,14365,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(36,14366,1,1,'io/hydrax/proto/metwo/match/PsOrder.getPremium')
f(36,14367,1,1,'io/hydrax/proto/metwo/match/PsOrder.getSide')
f(36,14368,2,1,'io/hydrax/proto/metwo/match/PsOrder.newBuilder')
f(37,14369,1,2,'io/hydrax/proto/metwo/match/PsOrder.toBuilder',1,0,0)
f(36,14370,6,1,'io/hydrax/proto/metwo/match/Request$Builder.build')
f(37,14370,6,1,'io/hydrax/proto/metwo/match/Request$Builder.buildPartial')
f(36,14376,1,1,'io/hydrax/proto/metwo/match/Request$Builder.setFromService')
f(37,14376,1,2,'com/google/protobuf/GeneratedMessageV3$Builder.onChanged',1,0,0)
f(36,14377,1,1,'io/hydrax/proto/metwo/match/Request$Builder.setPsOrder')
f(36,14378,3,1,'io/hydrax/proto/metwo/match/Request.newBuilder')
f(37,14378,3,1,'io/hydrax/proto/metwo/match/Request.toBuilder')
f(38,14378,2,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(39,14378,2,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(40,14378,2,4,'MemAllocator::allocate() const')
f(41,14378,1,4,'ParallelScavengeHeap::mem_allocate(unsigned long, bool*)')
f(42,14378,1,4,'AllocTracer::send_allocation_requiring_gc_event(unsigned long, unsigned int)')
f(41,14379,1,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(38,14380,1,4,'SharedRuntime::on_slowpath_allocation_exit(JavaThread*)')
f(36,14381,1,1,'io/hydrax/proto/metwo/match/Response$Builder.setPsParentOrderExecReport')
f(36,14382,1,1,'io/hydrax/proto/metwo/match/Response.newBuilder')
f(36,14383,7,1,'io/hydrax/proto/metwo/match/ResponseList$Builder.build')
f(37,14384,6,2,'io/hydrax/proto/metwo/match/ResponseList$Builder.buildPartial',6,0,0)
f(36,14390,1,1,'java/math/BigDecimal.add')
f(36,14391,1,1,'java/util/concurrent/atomic/AtomicLong.getAndIncrement')
f(31,14392,12,1,'java/util/stream/ReferencePipeline.findFirst',2,0,0)
f(32,14392,12,1,'java/util/stream/AbstractPipeline.evaluate',2,0,0)
f(33,14392,12,1,'java/util/stream/FindOps$FindOp.evaluateSequential',2,0,0)
f(34,14392,12,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',2,0,0)
f(35,14392,10,1,'java/util/stream/AbstractPipeline.copyInto',1,0,0)
f(36,14393,9,1,'java/util/stream/AbstractPipeline.copyIntoWithCancel')
f(37,14394,1,3,'itable stub')
f(37,14395,2,1,'java/util/Spliterator.getExactSizeIfKnown')
f(38,14395,2,1,'java/util/AbstractList$RandomAccessSpliterator.estimateSize')
f(37,14397,5,1,'java/util/stream/ReferencePipeline.forEachWithCancel',1,0,0)
f(38,14398,4,1,'java/util/AbstractList$RandomAccessSpliterator.tryAdvance')
f(39,14399,3,2,'java/util/stream/ReferencePipeline$2$1.accept',3,0,0)
f(40,14400,2,3,'itable stub')
f(35,14402,2,2,'java/util/stream/AbstractPipeline.wrapSink',1,0,0)
f(36,14403,1,1,'java/util/stream/ReferencePipeline$2.opWrapSink')
f(29,14404,3,1,'java/util/AbstractCollection.retainAll')
f(30,14404,1,1,'java/util/HashMap$HashIterator.hasNext')
f(30,14405,1,1,'java/util/HashSet.iterator')
f(31,14405,1,1,'java/util/HashMap$KeySet.iterator')
f(32,14405,1,1,'java/util/HashMap$KeyIterator.<init>')
f(33,14405,1,1,'java/util/HashMap$HashIterator.<init>')
f(30,14406,1,1,'java/util/ImmutableCollections$AbstractImmutableList.contains')
f(29,14407,4,1,'java/util/ArrayList.<init>')
f(30,14407,1,3,'itable stub')
f(30,14408,3,1,'java/util/HashSet.toArray')
f(29,14411,5,2,'java/util/HashSet.<init>',4,0,0)
f(30,14411,5,2,'java/util/AbstractCollection.addAll',4,0,0)
f(31,14411,5,2,'java/util/HashSet.add',4,0,0)
f(32,14411,5,2,'java/util/HashMap.put',4,0,0)
f(33,14411,5,2,'java/util/HashMap.putVal',4,0,0)
f(34,14415,1,1,'java/util/HashMap.resize')
f(29,14416,329,1,'java/util/stream/ReferencePipeline.findFirst',3,0,0)
f(30,14416,329,1,'java/util/stream/AbstractPipeline.evaluate',3,0,0)
f(31,14416,329,1,'java/util/stream/FindOps$FindOp.evaluateSequential',3,0,0)
f(32,14416,329,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',3,0,0)
f(33,14416,325,1,'java/util/stream/AbstractPipeline.copyInto')
f(34,14416,325,1,'java/util/stream/AbstractPipeline.copyIntoWithCancel')
f(35,14416,1,3,'itable stub')
f(35,14417,3,1,'java/util/Spliterator.getExactSizeIfKnown')
f(36,14418,1,1,'java/util/ArrayList$ArrayListSpliterator.characteristics')
f(36,14419,1,1,'java/util/ArrayList$ArrayListSpliterator.estimateSize')
f(35,14420,2,1,'java/util/stream/ReferencePipeline$2$1.begin')
f(36,14420,2,3,'itable stub')
f(35,14422,319,1,'java/util/stream/ReferencePipeline.forEachWithCancel',21,0,0)
f(36,14425,14,3,'itable stub')
f(36,14439,298,1,'java/util/ArrayList$ArrayListSpliterator.tryAdvance')
f(37,14452,285,1,'java/util/stream/ReferencePipeline$2$1.accept')
f(38,14455,266,1,'io/hydrax/pricestreaming/router/RoutingEngine$$Lambda.0x00007f9eb7c8f030.test')
f(39,14467,254,2,'io/hydrax/pricestreaming/router/RoutingEngine.lambda$route$1',219,0,0)
f(40,14468,252,2,'io/hydrax/pricestreaming/router/Rule.match',218,0,0)
f(41,14468,74,2,'io/hydrax/pricestreaming/domain/OrderRoutingStrategyDTO.getTimeInForces',74,0,0)
f(42,14468,74,2,'java/util/HashMap.get',74,0,0)
f(43,14468,74,2,'java/util/HashMap.getNode',74,0,0)
f(41,14542,1,1,'io/hydrax/proto/metwo/match/PsOrder.getOrdType')
f(42,14542,1,1,'io/hydrax/proto/metwo/match/PsOrderType.valueOf')
f(43,14542,1,1,'io/hydrax/proto/metwo/match/PsOrderType.forNumber')
f(44,14542,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(45,14542,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(46,14542,1,4,'MemAllocator::allocate() const')
f(47,14542,1,4,'ObjAllocator::initialize(HeapWordImpl**) const')
f(41,14543,77,2,'java/util/AbstractCollection.contains',44,0,0)
f(42,14543,45,2,'com/google/protobuf/UnmodifiableLazyStringList$2.hasNext',31,0,0)
f(43,14543,45,2,'java/util/AbstractList$Itr.hasNext',31,0,0)
f(44,14574,14,1,'com/google/protobuf/LazyStringArrayList.size')
f(42,14588,30,2,'com/google/protobuf/UnmodifiableLazyStringList$2.next',12,0,0)
f(43,14588,30,2,'com/google/protobuf/UnmodifiableLazyStringList$2.next',12,0,0)
f(44,14588,30,2,'java/util/AbstractList$Itr.next',12,0,0)
f(45,14600,18,1,'com/google/protobuf/LazyStringArrayList.get')
f(46,14603,15,2,'com/google/protobuf/LazyStringArrayList.get',15,0,0)
f(47,14603,15,2,'java/util/ArrayList.get',15,0,0)
f(48,14603,15,2,'java/util/Objects.checkIndex',15,0,0)
f(42,14618,2,2,'java/util/AbstractList$Itr.hasNext',1,0,0)
f(43,14619,1,1,'com/google/protobuf/LazyStringArrayList.size')
f(41,14620,100,2,'java/util/HashSet.contains',100,0,0)
f(42,14620,100,2,'java/util/HashMap.containsKey',100,0,0)
f(43,14620,100,2,'java/util/HashMap.getNode',100,0,0)
f(44,14630,90,2,'java/util/HashMap$TreeNode.getTreeNode',90,0,0)
f(40,14720,1,1,'java/util/Collections.disjoint')
f(41,14720,1,2,'java/util/HashSet.contains',1,0,0)
f(42,14720,1,2,'java/util/HashMap.containsKey',1,0,0)
f(43,14720,1,2,'java/util/HashMap.getNode',1,0,0)
f(38,14721,16,3,'itable stub')
f(36,14737,4,2,'java/util/concurrent/ConcurrentHashMap$ValueSpliterator.tryAdvance',4,0,0)
f(37,14737,4,2,'java/util/concurrent/ConcurrentHashMap$Traverser.advance',4,0,0)
f(33,14741,4,2,'java/util/stream/AbstractPipeline.wrapSink',3,0,0)
f(34,14742,1,1,'java/util/stream/ReferencePipeline$2.opWrapSink')
f(35,14742,1,2,'java/util/stream/ReferencePipeline$2$1.<init>',1,0,0)
f(36,14742,1,2,'java/util/stream/Sink$ChainedReference.<init>',1,0,0)
f(34,14743,2,3,'jint_disjoint_arraycopy_avx3')
f(27,14745,1634,1,'io/hydrax/pricestreaming/service/OrderService.marketFilter',8,0,0)
f(28,14745,1604,1,'io/hydrax/pricestreaming/cache/TradingVenueCache.selectCodeByTimeInForceAndOrderType',5,0,0)
f(29,14750,1599,1,'java/util/stream/ReferencePipeline.toList')
f(30,14750,1599,1,'java/util/stream/ReferencePipeline.toArray',14,0,0)
f(31,14750,1599,1,'java/util/stream/ReferencePipeline.toArray',14,0,0)
f(32,14750,1595,1,'java/util/stream/AbstractPipeline.evaluateToArrayNode',10,0,0)
f(33,14750,1595,1,'java/util/stream/AbstractPipeline.evaluate',10,0,0)
f(34,14750,1595,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',10,0,0)
f(35,14750,1588,1,'java/util/stream/AbstractPipeline.copyInto',6,0,0)
f(36,14751,5,3,'itable stub')
f(36,14756,2,1,'java/util/Spliterator.getExactSizeIfKnown')
f(37,14756,2,1,'java/util/concurrent/ConcurrentHashMap$ValueSpliterator.characteristics')
f(36,14758,1572,1,'java/util/concurrent/ConcurrentHashMap$ValueSpliterator.forEachRemaining')
f(37,14761,110,2,'java/util/concurrent/ConcurrentHashMap$Traverser.advance',110,0,0)
f(37,14871,1459,1,'java/util/stream/ReferencePipeline$2$1.accept',55,0,0)
f(38,14904,1400,1,'io/hydrax/pricestreaming/cache/TradingVenueCache$$Lambda.0x00007f9eb7c85800.test')
f(39,14926,1378,2,'io/hydrax/pricestreaming/cache/TradingVenueCache.lambda$selectCodeByTimeInForceAndOrderType$4',564,0,0)
f(40,14926,64,2,'io/hydrax/pricestreaming/common/OrderType.from',59,0,0)
f(41,14926,64,2,'org/agrona/collections/Object2ObjectHashMap.get',59,0,0)
f(42,14926,64,2,'org/agrona/collections/Object2ObjectHashMap.getMapped',59,0,0)
f(43,14926,5,1,'java/lang/String.hashCode')
f(44,14926,5,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(45,14927,4,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(46,14927,4,4,'MemAllocator::allocate() const')
f(47,14927,4,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(43,14931,59,2,'java/util/Objects.equals',59,0,0)
f(40,14990,33,2,'io/hydrax/pricestreaming/common/TimeInForceEnum.from',33,0,0)
f(41,14990,33,2,'org/agrona/collections/Object2ObjectHashMap.get',33,0,0)
f(42,14990,33,2,'org/agrona/collections/Object2ObjectHashMap.getMapped',33,0,0)
f(43,14990,33,2,'java/util/Objects.equals',33,0,0)
f(40,15023,2,1,'java/util/Collections$UnmodifiableCollection.stream')
f(41,15023,2,1,'java/util/Collection.stream')
f(42,15023,2,1,'java/util/stream/StreamSupport.stream')
f(43,15023,2,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(44,15024,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(45,15024,1,4,'MemAllocator::allocate() const')
f(46,15024,1,4,'AllocTracer::send_allocation_in_new_tlab(Klass*, HeapWordImpl**, unsigned long, unsigned long, JavaThread*)')
f(47,15024,1,4,'JfrObjectAllocationSample::send_event(Klass const*, unsigned long, bool, Thread*)')
f(40,15025,27,2,'java/util/HashMap.get',27,0,0)
f(41,15025,27,2,'java/util/HashMap.getNode',27,0,0)
f(40,15052,322,2,'java/util/HashSet.contains',322,0,0)
f(41,15052,322,2,'java/util/HashMap.containsKey',322,0,0)
f(42,15052,322,2,'java/util/HashMap.getNode',322,0,0)
f(43,15124,250,2,'java/util/HashMap$TreeNode.getTreeNode',250,0,0)
f(40,15374,4,1,'java/util/Optional.ofNullable')
f(41,15374,4,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(42,15374,4,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(43,15374,4,4,'MemAllocator::allocate() const')
f(44,15374,2,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(45,15375,1,4,'ThreadLocalAllocBuffer::fill(HeapWordImpl**, HeapWordImpl**, unsigned long)')
f(44,15376,2,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(40,15378,920,1,'java/util/stream/ReferencePipeline.collect',123,0,0)
f(41,15378,920,1,'java/util/stream/AbstractPipeline.evaluate',123,0,0)
f(42,15378,920,1,'java/util/stream/ReduceOps$ReduceOp.evaluateSequential',123,0,0)
f(43,15378,910,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',123,0,0)
f(44,15378,763,1,'java/util/stream/AbstractPipeline.copyInto',24,0,0)
f(45,15402,409,1,'java/util/ArrayList$ArrayListSpliterator.forEachRemaining')
f(46,15465,19,3,'itable stub')
f(46,15484,4,1,'java/util/stream/Collectors$$Lambda.0x800000039.accept')
f(47,15484,4,2,'java/util/HashSet.add',4,0,0)
f(48,15484,4,2,'java/util/HashMap.put',4,0,0)
f(49,15484,4,2,'java/util/HashMap.putVal',4,0,0)
f(46,15488,323,1,'java/util/stream/ReferencePipeline$3$1.accept')
f(47,15508,1,4,'SafepointSynchronize::handle_polling_page_exception(JavaThread*)')
f(48,15508,1,4,'ThreadSafepointState::handle_polling_page_exception()')
f(49,15508,1,4,'ImmutableOopMapSet::find_map_at_offset(int) const')
f(47,15509,28,1,'io/hydrax/pricestreaming/cache/TradingVenueCache$$Lambda.0x00007f9eb7c85c78.apply')
f(47,15537,39,3,'itable stub')
f(47,15576,235,1,'java/util/stream/ReduceOps$3ReducingSink.accept')
f(48,15585,19,3,'itable stub')
f(48,15604,207,1,'java/util/stream/Collectors$$Lambda.0x800000039.accept')
f(49,15620,191,2,'java/util/HashSet.add',67,0,0)
f(50,15620,191,2,'java/util/HashMap.put',67,0,0)
f(51,15620,7,1,'java/util/HashMap.hash',1,0,0)
f(52,15620,6,1,'java/lang/String.hashCode')
f(52,15626,1,3,'vtable stub')
f(51,15627,184,2,'java/util/HashMap.putVal',66,0,0)
f(52,15693,9,1,'java/util/HashMap.afterNodeInsertion')
f(52,15702,30,1,'java/util/HashMap.newNode')
f(53,15731,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(54,15731,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(55,15731,1,4,'MemAllocator::allocate() const')
f(56,15731,1,4,'ObjAllocator::initialize(HeapWordImpl**) const')
f(52,15732,79,1,'java/util/HashMap.resize')
f(53,15797,13,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(54,15798,12,4,'InstanceKlass::allocate_objArray(int, int, JavaThread*)')
f(55,15799,1,4,'InstanceKlass::array_klass(int, JavaThread*)')
f(55,15800,9,4,'MemAllocator::allocate() const')
f(56,15800,2,4,'AllocTracer::send_allocation_in_new_tlab(Klass*, HeapWordImpl**, unsigned long, unsigned long, JavaThread*)')
f(57,15800,1,4,'JfrObjectAllocationSample::send_event(Klass const*, unsigned long, bool, Thread*)')
f(57,15801,1,4,'ObjectSampler::is_created()')
f(56,15802,2,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(57,15803,1,4,'ThreadLocalAllocBuffer::retire_before_allocation()')
f(56,15804,3,4,'ObjArrayAllocator::initialize(HeapWordImpl**) const')
f(56,15807,1,4,'ParallelScavengeHeap::allocate_new_tlab(unsigned long, unsigned long, unsigned long*)')
f(56,15808,1,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(55,15809,1,4,'ObjArrayAllocator::initialize(HeapWordImpl**) const')
f(53,15810,1,4,'oopFactory::new_objArray(Klass*, int, JavaThread*)')
f(45,15811,102,1,'java/util/Spliterator.getExactSizeIfKnown')
f(46,15816,60,3,'itable stub')
f(46,15876,14,1,'java/util/ArrayList$ArrayListSpliterator.characteristics')
f(46,15890,23,1,'java/util/ArrayList$ArrayListSpliterator.estimateSize')
f(47,15908,5,2,'java/util/ArrayList$ArrayListSpliterator.getFence',5,0,0)
f(45,15913,154,1,'java/util/stream/Sink$ChainedReference.begin')
f(46,15927,24,3,'itable stub')
f(46,15951,116,1,'java/util/stream/ReduceOps$3ReducingSink.begin')
f(47,15966,25,3,'itable stub')
f(47,15991,76,1,'java/util/stream/Collectors$$Lambda.0x800000045.get')
f(48,16059,8,1,'java/util/HashSet.<init>')
f(49,16059,7,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(50,16061,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(50,16062,4,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(51,16062,3,4,'MemAllocator::allocate() const')
f(52,16062,1,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(53,16062,1,4,'ThreadLocalAllocBuffer::retire_before_allocation()')
f(54,16062,1,4,'CollectedHeap::fill_with_dummy_object(HeapWordImpl**, HeapWordImpl**, bool)')
f(52,16063,1,4,'ObjAllocator::initialize(HeapWordImpl**) const')
f(52,16064,1,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(51,16065,1,4,'ObjAllocator::initialize(HeapWordImpl**) const')
f(49,16066,1,4,'SharedRuntime::on_slowpath_allocation_exit(JavaThread*)')
f(45,16067,74,1,'java/util/stream/Sink$ChainedReference.end')
f(46,16083,50,3,'itable stub')
f(46,16133,8,1,'java/util/stream/Sink.end')
f(44,16141,147,2,'java/util/stream/AbstractPipeline.wrapSink',99,0,0)
f(45,16240,48,1,'java/util/stream/ReferencePipeline$3.opWrapSink')
f(46,16259,29,2,'java/util/stream/ReferencePipeline$3$1.<init>',29,0,0)
f(47,16259,29,2,'java/util/stream/Sink$ChainedReference.<init>',29,0,0)
f(43,16288,10,1,'java/util/stream/ReduceOps$3.makeSink')
f(44,16288,10,1,'java/util/stream/ReduceOps$3.makeSink')
f(45,16288,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(45,16289,9,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(46,16289,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(46,16290,5,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(47,16290,5,4,'MemAllocator::allocate() const')
f(48,16290,2,4,'MemAllocator::mem_allocate_inside_tlab_slow(MemAllocator::Allocation&) const')
f(49,16290,1,4,'ThreadLocalAllocBuffer::fill(HeapWordImpl**, HeapWordImpl**, unsigned long)')
f(49,16291,1,4,'ThreadLocalAllocBuffer::retire_before_allocation()')
f(50,16291,1,4,'HandleMark::initialize(Thread*)')
f(48,16292,1,4,'ParallelScavengeHeap::mem_allocate(unsigned long, bool*)')
f(49,16292,1,4,'Mutex::lock()')
f(50,16292,1,3,'__lll_lock_wait')
f(48,16293,2,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(46,16295,3,4,'PSCardTable::is_in_young(void const*) const')
f(40,16298,6,1,'java/util/stream/ReferencePipeline.map')
f(41,16298,5,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(42,16298,4,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(43,16298,4,4,'MemAllocator::allocate() const')
f(44,16298,1,4,'MutableSpace::cas_allocate(unsigned long)')
f(44,16299,1,4,'ParallelScavengeHeap::unsafe_max_tlab_alloc(Thread*) const')
f(44,16300,2,4,'ThreadLocalAllocBuffer::fill(HeapWordImpl**, HeapWordImpl**, unsigned long)')
f(42,16302,1,4,'PSCardTable::is_in_young(void const*) const')
f(41,16303,1,4,'SharedRuntime::on_slowpath_allocation_exit(JavaThread*)')
f(38,16304,22,3,'itable stub')
f(38,16326,4,1,'java/util/stream/ReferencePipeline$3$1.accept')
f(39,16327,2,3,'itable stub')
f(39,16329,1,1,'java/util/stream/Nodes$SpinedNodeBuilder.accept')
f(40,16329,1,2,'java/util/stream/SpinedBuffer.accept',1,0,0)
f(41,16329,1,2,'java/util/stream/SpinedBuffer.increaseCapacity',1,0,0)
f(36,16330,2,1,'java/util/stream/ReferencePipeline$2$1.begin')
f(37,16330,2,1,'java/util/stream/Sink$ChainedReference.begin')
f(38,16331,1,1,'java/util/stream/Nodes$SpinedNodeBuilder.begin')
f(36,16332,6,1,'java/util/stream/Sink$ChainedReference.end')
f(37,16336,2,1,'java/util/stream/Sink$ChainedReference.end')
f(38,16337,1,3,'itable stub')
f(35,16338,7,2,'java/util/stream/AbstractPipeline.wrapSink',4,0,0)
f(36,16341,3,1,'java/util/stream/ReferencePipeline$3.opWrapSink')
f(37,16343,1,2,'java/util/stream/ReferencePipeline$3$1.<init>',1,0,0)
f(38,16343,1,2,'java/util/stream/Sink$ChainedReference.<init>',1,0,0)
f(36,16344,1,3,'vtable stub')
f(32,16345,4,2,'java/util/stream/Nodes.flatten',4,0,0)
f(33,16348,1,3,'jint_disjoint_arraycopy_avx3')
f(28,16349,13,1,'io/hydrax/pricestreaming/common/OrderType.from',3,0,0)
f(29,16349,12,1,'java/util/stream/ReferencePipeline.findFirst',3,0,0)
f(30,16349,12,1,'java/util/stream/AbstractPipeline.evaluate',3,0,0)
f(31,16349,12,1,'java/util/stream/FindOps$FindOp.evaluateSequential',3,0,0)
f(32,16349,12,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto',3,0,0)
f(33,16349,9,1,'java/util/stream/AbstractPipeline.copyInto')
f(34,16349,9,1,'java/util/stream/AbstractPipeline.copyIntoWithCancel')
f(35,16349,2,1,'java/util/Spliterator.getExactSizeIfKnown')
f(36,16350,1,1,'java/util/Spliterators$ArraySpliterator.characteristics')
f(35,16351,1,1,'java/util/stream/ReferencePipeline$2$1.begin')
f(36,16351,1,3,'itable stub')
f(35,16352,6,1,'java/util/stream/ReferencePipeline.forEachWithCancel')
f(36,16352,6,1,'java/util/Spliterators$ArraySpliterator.tryAdvance')
f(37,16353,5,1,'java/util/stream/ReferencePipeline$2$1.accept')
f(38,16353,2,1,'io/hydrax/pricestreaming/common/OrderType$$Lambda.0x00007f9eb7c87cf8.test')
f(38,16355,3,3,'itable stub')
f(33,16358,3,2,'java/util/stream/AbstractPipeline.wrapSink',3,0,0)
f(29,16361,1,1,'java/util/stream/Stream.of')
f(30,16361,1,1,'java/util/Arrays.stream')
f(31,16361,1,1,'java/util/Arrays.stream')
f(32,16361,1,1,'java/util/Arrays.spliterator')
f(33,16361,1,1,'java/util/Spliterators.spliterator')
f(34,16361,1,4,'OptoRuntime::new_array_C(Klass*, int, JavaThread*)')
f(35,16361,1,4,'InstanceKlass::allocate_objArray(int, int, JavaThread*)')
f(36,16361,1,3,'__tls_get_addr')
f(28,16362,17,1,'io/hydrax/pricestreaming/common/TimeInForceEnum.from')
f(29,16362,1,1,'io/hydrax/pricestreaming/common/TimeInForceEnum.values')
f(30,16362,1,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(31,16362,1,4,'CardTableBarrierSet::on_slowpath_allocation_exit(JavaThread*, oopDesc*)')
f(29,16363,15,1,'java/util/stream/ReferencePipeline.findFirst')
f(30,16363,15,1,'java/util/stream/AbstractPipeline.evaluate')
f(31,16363,15,1,'java/util/stream/FindOps$FindOp.evaluateSequential')
f(32,16363,15,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(33,16363,15,1,'java/util/stream/AbstractPipeline.copyInto')
f(34,16363,15,1,'java/util/stream/AbstractPipeline.copyIntoWithCancel')
f(35,16363,5,3,'itable stub')
f(35,16368,1,1,'java/util/stream/ReferencePipeline$2$1.begin')
f(36,16368,1,3,'itable stub')
f(35,16369,8,2,'java/util/stream/ReferencePipeline.forEachWithCancel',5,0,0)
f(36,16369,1,3,'itable stub')
f(36,16370,3,1,'java/util/Spliterators$ArraySpliterator.tryAdvance')
f(37,16370,3,1,'java/util/stream/ReferencePipeline$2$1.accept')
f(38,16370,1,1,'io/hydrax/pricestreaming/common/TimeInForceEnum$$Lambda.0x00007f9eb7c87ab0.test')
f(38,16371,2,3,'itable stub')
f(36,16373,3,2,'java/util/concurrent/ConcurrentHashMap$ValueSpliterator.tryAdvance',3,0,0)
f(37,16373,3,2,'java/util/concurrent/ConcurrentHashMap$Traverser.advance',3,0,0)
f(36,16376,1,2,'java/util/stream/Sink$ChainedReference.cancellationRequested',1,0,0)
f(37,16376,1,2,'java/util/stream/ReferencePipeline$7$1.cancellationRequested',1,0,0)
f(38,16376,1,2,'java/util/stream/Sink$ChainedReference.cancellationRequested',1,0,0)
f(39,16376,1,2,'java/util/stream/ReferencePipeline$7$1.cancellationRequested',1,0,0)
f(35,16377,1,1,'java/util/stream/Sink$ChainedReference.end')
f(36,16377,1,1,'java/util/stream/Sink.end')
f(29,16378,1,1,'java/util/stream/Stream.of')
f(30,16378,1,1,'java/util/Arrays.stream')
f(31,16378,1,1,'java/util/Arrays.stream')
f(32,16378,1,1,'java/util/stream/StreamSupport.stream')
f(33,16378,1,3,'__pthread_mutex_trylock')
f(26,16379,5,2,'io/hydrax/pricestreaming/service/OrderService_ClientProxy.arc$delegate',5,0,0)
f(27,16379,5,2,'io/quarkus/arc/impl/ClientProxies.getApplicationScopedDelegate',5,0,0)
f(28,16379,5,2,'io/quarkus/arc/impl/AbstractSharedContext.get',5,0,0)
f(29,16380,4,2,'io/quarkus/arc/impl/Scopes.scopeMatches',4,0,0)
f(24,16384,4,2,'io/hydrax/pricestreaming/events/OrderEvent_ClientProxy.arc$delegate',4,0,0)
f(25,16384,4,2,'io/quarkus/arc/impl/ClientProxies.getApplicationScopedDelegate',4,0,0)
f(26,16384,4,2,'io/quarkus/arc/impl/AbstractSharedContext.get',4,0,0)
f(27,16384,4,2,'io/quarkus/arc/impl/Scopes.scopeMatches',4,0,0)
f(23,16388,1,1,'io/quarkus/arc/impl/CreationalContextImpl.release')
f(21,16389,2,3,'itable stub')
f(18,16391,64,1,'io/vertx/core/impl/ContextInternal.beginDispatch',2,0,0)
f(19,16391,64,1,'io/vertx/core/impl/VertxImpl.beginDispatch',2,0,0)
f(20,16393,49,4,'SharedRuntime::notify_jvmti_vthread_start(oopDesc*, unsigned char, JavaThread*)')
f(21,16393,1,4,'JvmtiEventController::thread_started(JavaThread*)')
f(21,16394,45,4,'JvmtiVTMSTransitionDisabler::VTMS_vthread_start(_jobject*)')
f(22,16394,3,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<548964ul, CardTableBarrierSet>, (AccessInternal::BarrierType)2, 548964ul>::oop_access_barrier(void*)')
f(22,16397,30,4,'JvmtiEventController::thread_started(JavaThread*)')
f(23,16400,1,4,'AllocateHeap(unsigned long, MEMFLAGS, AllocFailStrategy::AllocFailEnum)')
f(24,16400,1,3,'__malloc')
f(23,16401,6,4,'JvmtiEnvBase::is_valid()')
f(23,16407,1,4,'JvmtiEnvThreadState::has_frame_pops()')
f(23,16408,4,4,'JvmtiEventControllerPrivate::recompute_thread_enabled(JvmtiThreadState*)')
f(23,16412,14,4,'JvmtiThreadState::JvmtiThreadState(JavaThread*, oopDesc*)')
f(24,16417,1,4,'AllocateHeap(unsigned long, MEMFLAGS, AllocFailStrategy::AllocFailEnum)')
f(25,16417,1,3,'__malloc')
f(24,16418,8,4,'OopStorage::allocate()')
f(25,16421,5,3,'pthread_mutex_lock')
f(23,16426,1,3,'__tls_get_addr')
f(22,16427,10,4,'JvmtiVTMSTransitionDisabler::VTMS_mount_end(_jobject*)')
f(23,16427,1,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<548964ul, CardTableBarrierSet>, (AccessInternal::BarrierType)0, 548964ul>::oop_access_barrier(void*, oopDesc*)')
f(23,16428,3,4,'JavaThread::rebind_to_jvmti_thread_state_of(oopDesc*)')
f(23,16431,1,4,'JvmtiThreadState::set_thread(JavaThread*)')
f(23,16432,4,4,'Mutex::lock()')
f(24,16432,3,4,'JavaThread::is_active_Java_thread() const')
f(24,16435,1,3,'__lll_lock_wait')
f(23,16436,1,3,'__pthread_mutex_trylock')
f(22,16437,1,4,'JvmtiVTMSTransitionDisabler::finish_VTMS_transition(_jobject*, bool)')
f(22,16438,1,3,'__pthread_mutex_unlock_usercnt')
f(21,16439,1,4,'Mutex::unlock()')
f(21,16440,1,3,'__lll_unlock_wake')
f(21,16441,1,3,'__pthread_mutex_unlock_usercnt')
f(20,16442,6,3,'__malloc')
f(20,16448,2,3,'__pthread_mutex_trylock')
f(20,16450,5,1,'io/vertx/core/impl/VertxImpl.beginDispatch2')
f(21,16450,5,2,'java/lang/ThreadLocal.get',3,0,0)
f(22,16450,5,2,'java/lang/ThreadLocal.get',3,0,0)
f(23,16450,5,2,'java/lang/ThreadLocal.setInitialValue',3,0,0)
f(24,16450,3,2,'java/lang/ThreadLocal$ThreadLocalMap.set',3,0,0)
f(24,16453,2,1,'java/lang/ThreadLocal.createMap')
f(25,16453,2,1,'java/lang/ThreadLocal$ThreadLocalMap.<init>')
f(26,16453,2,4,'OptoRuntime::new_instance_C(Klass*, JavaThread*)')
f(27,16453,1,4,'InstanceKlass::allocate_instance(JavaThread*)')
f(28,16453,1,4,'MemAllocator::allocate() const')
f(29,16453,1,4,'ParallelScavengeHeap::allocate_new_tlab(unsigned long, unsigned long, unsigned long*)')
f(27,16454,1,4,'PSCardTable::is_in_young(void const*) const')
f(18,16455,12,1,'io/vertx/core/impl/ContextInternal.endDispatch')
f(19,16455,12,1,'io/vertx/core/impl/VertxImpl.endDispatch')
f(20,16455,12,1,'io/vertx/core/impl/VertxImpl.endDispatch2')
f(21,16455,12,1,'java/lang/ThreadLocal.remove',2,0,0)
f(22,16455,12,1,'java/lang/ThreadLocal.remove',2,0,0)
f(23,16455,12,1,'java/lang/ThreadLocal$ThreadLocalMap.remove',2,0,0)
f(24,16455,12,1,'java/lang/ref/Reference.clear',2,0,0)
f(25,16456,11,1,'java/lang/ref/Reference.clear0',1,0,0)
f(26,16458,4,4,'AccessInternal::PostRuntimeDispatch<CardTableBarrierSet::AccessBarrier<397414ul, CardTableBarrierSet>, (AccessInternal::BarrierType)1, 397414ul>::oop_access_barrier(oopDesc*, long, oopDesc*)')
f(26,16462,5,3,'JVM_ReferenceClear')
f(27,16465,2,4,'ThreadInVMfromNative::ThreadInVMfromNative(JavaThread*)')
f(17,16467,11,1,'java/util/concurrent/ThreadPerTaskExecutor.taskComplete',1,0,0)
f(18,16467,11,1,'java/util/concurrent/ConcurrentHashMap$KeySetView.remove',1,0,0)
f(19,16467,11,1,'java/util/concurrent/ConcurrentHashMap.remove',1,0,0)
f(20,16468,10,1,'java/util/concurrent/ConcurrentHashMap.replaceNode')
f(21,16469,1,1,'java/lang/VirtualThread.hashCode')
f(21,16470,8,2,'java/util/concurrent/ConcurrentHashMap.addCount',8,0,0)
f(10,16478,1,2,'jdk/internal/vm/Continuation.mount',1,0,0)
f(11,16478,1,2,'jdk/internal/vm/Continuation.compareAndSetMounted',1,0,0)
f(12,16478,1,2,'jdk/internal/misc/Unsafe.compareAndSetBoolean',1,0,0)
f(4,16479,29,1,'java/util/concurrent/ForkJoinPool.signalWork',9,0,0)
f(5,16481,27,2,'java/util/concurrent/locks/LockSupport.unpark',9,0,0)
f(6,16490,18,1,'jdk/internal/misc/Unsafe.unpark')
f(7,16491,1,4,'ThreadsListHandle::~ThreadsListHandle()')
f(7,16492,9,3,'Unsafe_Unpark')
f(8,16492,3,4,'FastThreadsListHandle::FastThreadsListHandle(oopDesc*, JavaThread*)')
f(8,16495,2,4,'ThreadsListHandle::~ThreadsListHandle()')
f(8,16497,1,3,'__tls_get_addr')
f(8,16498,3,3,'pthread_mutex_lock')
f(7,16501,7,3,'__pthread_cond_signal')
f(1,16508,1,3,'nanosleep')
f(1,16509,1919,3,'start_thread')
f(2,16509,1919,3,'thread_native_entry(Thread*)')
f(3,16509,1919,4,'Thread::call_run()')
f(4,16509,35,4,'JavaThread::thread_main_inner()')
f(5,16509,35,4,'CompileBroker::compiler_thread_loop()')
f(6,16509,35,4,'CompileBroker::invoke_compiler_on_method(CompileTask*)')
f(7,16509,32,4,'C2Compiler::compile_method(ciEnv*, ciMethod*, int, bool, DirectiveSet*)')
f(8,16509,32,4,'Compile::Compile(ciEnv*, ciMethod*, int, Options, DirectiveSet*)')
f(9,16509,1,4,'CallGenerator::for_inline(ciMethod*, float)')
f(10,16509,1,4,'InlineTree::check_can_parse(ciMethod*)')
f(11,16509,1,4,'ciMethod::get_flow_analysis()')
f(12,16509,1,4,'ciTypeFlow::do_flow()')
f(13,16509,1,4,'ciTypeFlow::flow_types()')
f(14,16509,1,4,'ciTypeFlow::df_flow_types(ciTypeFlow::Block*, bool, ciTypeFlow::StateVector*, ciTypeFlow::JsrSet*)')
f(15,16509,1,4,'ciTypeFlow::flow_block(ciTypeFlow::Block*, ciTypeFlow::StateVector*, ciTypeFlow::JsrSet*)')
f(16,16509,1,4,'ciTypeFlow::StateVector::apply_one_bytecode(ciBytecodeStream*)')
f(9,16510,15,4,'Compile::Code_Gen()')
f(10,16510,3,4,'Matcher::match()')
f(11,16510,1,4,'Arena::contains(void const*) const')
f(11,16511,1,4,'Matcher::find_shared(Node*)')
f(12,16511,1,4,'MultiNode::ideal_reg() const')
f(11,16512,1,4,'Matcher::xform(Node*, int)')
f(10,16513,3,4,'PhaseCFG::do_global_code_motion()')
f(11,16513,3,4,'PhaseCFG::global_code_motion()')
f(12,16513,1,4,'PhaseCFG::schedule_local(Block*, GrowableArray<int>&, VectorSet&, long*)')
f(13,16513,1,4,'PhaseChaitin::compute_entry_block_pressure(Block*)')
f(12,16514,1,4,'PhaseChaitin::gather_lrg_masks(bool)')
f(12,16515,1,4,'PhaseLive::compute(unsigned int)')
f(13,16515,1,4,'PhaseLive::add_liveout(Block_List&, Block*, IndexSet*, VectorSet&)')
f(10,16516,9,4,'PhaseChaitin::Register_Allocate()')
f(11,16516,4,4,'PhaseChaitin::Select()')
f(12,16517,1,4,'IndexSetIterator::advance_and_next()')
f(12,16518,2,4,'PhaseIFG::re_insert(unsigned int)')
f(11,16520,1,4,'PhaseChaitin::Split(unsigned int, ResourceArea*)')
f(12,16520,1,4,'Node::disconnect_inputs(Compile*)')
f(11,16521,1,4,'PhaseChaitin::gather_lrg_masks(bool) [clone .constprop.0]')
f(11,16522,2,4,'PhaseChaitin::post_allocate_copy_removal()')
f(12,16522,2,4,'PhaseChaitin::elide_copy(Node*, int, Block*, Node_List*, Node_List*, bool)')
f(11,16524,1,4,'PhaseChaitin::stretch_base_pointer_live_ranges(ResourceArea*)')
f(9,16525,10,4,'Compile::Optimize()')
f(10,16525,1,4,'ConnectionGraph::do_analysis(Compile*, PhaseIterGVN*)')
f(11,16525,1,4,'ConnectionGraph::compute_escape()')
f(12,16525,1,4,'ConnectionGraph::add_node_to_connection_graph(Node*, Unique_Node_List*)')
f(10,16526,2,4,'PhaseIdealLoop::build_and_optimize()')
f(11,16526,1,4,'PhaseIdealLoop::build_loop_tree()')
f(12,16526,1,4,'PhaseIdealLoop::build_loop_tree_impl(Node*, int)')
f(11,16527,1,4,'PhaseIdealLoop::split_if_with_blocks(VectorSet&, Node_Stack&)')
f(10,16528,2,4,'PhaseIdealLoop::optimize(PhaseIterGVN&, LoopOptsMode)')
f(11,16528,1,4,'PhaseIdealLoop::build_and_optimize()')
f(12,16528,1,4,'PhaseIdealLoop::build_loop_late(VectorSet&, Node_List&, Node_Stack&)')
f(13,16528,1,4,'PhaseIdealLoop::build_loop_late_post_work(Node*, bool)')
f(11,16529,1,4,'PhaseIterGVN::optimize()')
f(12,16529,1,4,'PhaseIterGVN::transform_old(Node*)')
f(13,16529,1,4,'RegionNode::Ideal(PhaseGVN*, bool)')
f(14,16529,1,4,'PhiNode::is_unsafe_data_reference(Node*) const [clone .part.0]')
f(10,16530,1,4,'PhaseIterGVN::PhaseIterGVN(PhaseGVN*)')
f(11,16530,1,4,'PhaseIterGVN::add_users_to_worklist(Node*)')
f(10,16531,4,4,'PhaseIterGVN::optimize()')
f(11,16531,4,4,'PhaseIterGVN::transform_old(Node*)')
f(12,16531,1,4,'IfNode::Ideal(PhaseGVN*, bool)')
f(13,16531,1,4,'IfNode::fold_compares(PhaseIterGVN*)')
f(14,16531,1,4,'IfNode::has_shared_region(ProjNode*, ProjNode*&, ProjNode*&)')
f(15,16531,1,4,'Node::unique_ctrl_out_or_null() const')
f(12,16532,1,4,'PhaseIterGVN::add_users_to_worklist(Node*)')
f(13,16532,1,4,'PhaseIterGVN::add_users_to_worklist0(Node*)')
f(12,16533,1,4,'PhiNode::Ideal(PhaseGVN*, bool)')
f(12,16534,1,4,'PhiNode::Value(PhaseGVN*) const')
f(13,16534,1,4,'Type::filter_helper(Type const*, bool) const')
f(14,16534,1,4,'Type::meet_helper(Type const*, bool) const')
f(9,16535,5,4,'ParseGenerator::generate(JVMState*)')
f(10,16535,5,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(11,16535,5,4,'Parse::do_all_blocks()')
f(12,16535,5,4,'Parse::do_one_block()')
f(13,16535,4,4,'Parse::do_call()')
f(14,16535,1,4,'Compile::call_generator(ciMethod*, int, bool, JVMState*, bool, float, ciKlass*, bool)')
f(15,16535,1,4,'InlineTree::ok_to_inline(ciMethod*, JVMState*, ciCallProfile&, bool&)')
f(16,16535,1,4,'ciMethod::get_flow_analysis()')
f(17,16535,1,4,'ciTypeFlow::do_flow()')
f(18,16535,1,4,'ciTypeFlow::flow_types()')
f(19,16535,1,4,'ciTypeFlow::df_flow_types(ciTypeFlow::Block*, bool, ciTypeFlow::StateVector*, ciTypeFlow::JsrSet*)')
f(20,16535,1,4,'ciTypeFlow::flow_block(ciTypeFlow::Block*, ciTypeFlow::StateVector*, ciTypeFlow::JsrSet*)')
f(21,16535,1,4,'AnyObj::operator new(unsigned long, Arena*)')
f(14,16536,3,4,'PredictedCallGenerator::generate(JVMState*)')
f(15,16536,3,4,'ParseGenerator::generate(JVMState*)')
f(16,16536,3,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(17,16536,3,4,'Parse::do_all_blocks()')
f(18,16536,3,4,'Parse::do_one_block()')
f(19,16536,3,4,'Parse::do_call()')
f(20,16536,2,4,'ParseGenerator::generate(JVMState*)')
f(21,16536,2,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(22,16536,2,4,'Parse::do_all_blocks()')
f(23,16536,2,4,'Parse::do_one_block()')
f(24,16536,2,4,'Parse::do_call()')
f(25,16536,2,4,'ParseGenerator::generate(JVMState*)')
f(26,16536,2,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(27,16536,2,4,'Parse::do_all_blocks()')
f(28,16536,2,4,'Parse::do_one_block()')
f(29,16536,2,4,'Parse::do_call()')
f(30,16536,1,4,'ParseGenerator::generate(JVMState*)')
f(31,16536,1,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(32,16536,1,4,'Parse::do_all_blocks()')
f(33,16536,1,4,'Parse::do_one_block()')
f(34,16536,1,4,'Parse::do_call()')
f(35,16536,1,4,'ParseGenerator::generate(JVMState*)')
f(36,16536,1,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(37,16536,1,4,'Parse::do_all_blocks()')
f(38,16536,1,4,'Parse::do_one_block()')
f(39,16536,1,4,'Parse::do_field_access(bool, bool)')
f(40,16536,1,4,'TypePtr::interfaces(ciKlass*&, bool, bool, bool, Type::InterfaceHandling)')
f(41,16536,1,4,'TypeInterfaces::make(GrowableArray<ciInstanceKlass*>*)')
f(30,16537,1,4,'PredictedCallGenerator::generate(JVMState*)')
f(31,16537,1,4,'ParseGenerator::generate(JVMState*)')
f(32,16537,1,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(33,16537,1,4,'Parse::do_all_blocks()')
f(34,16537,1,4,'Parse::do_one_block()')
f(35,16537,1,4,'Parse::do_call()')
f(36,16537,1,4,'PredictedCallGenerator::generate(JVMState*)')
f(37,16537,1,4,'PredictedCallGenerator::generate(JVMState*)')
f(38,16537,1,4,'ParseGenerator::generate(JVMState*)')
f(39,16537,1,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(40,16537,1,4,'Parse::do_all_blocks()')
f(41,16537,1,4,'Parse::do_one_block()')
f(42,16537,1,4,'Parse::do_call()')
f(43,16537,1,4,'PredictedCallGenerator::generate(JVMState*)')
f(44,16537,1,4,'ParseGenerator::generate(JVMState*)')
f(45,16537,1,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(46,16537,1,4,'Parse::do_all_blocks()')
f(47,16537,1,4,'Parse::do_one_block()')
f(48,16537,1,4,'Parse::do_call()')
f(49,16537,1,4,'PredictedCallGenerator::generate(JVMState*)')
f(50,16537,1,4,'ParseGenerator::generate(JVMState*)')
f(51,16537,1,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(52,16537,1,4,'Parse::do_all_blocks()')
f(53,16537,1,4,'Parse::do_one_block()')
f(54,16537,1,4,'Parse::do_call()')
f(55,16537,1,4,'Compile::call_generator(ciMethod*, int, bool, JVMState*, bool, float, ciKlass*, bool)')
f(56,16537,1,4,'Compile::call_generator(ciMethod*, int, bool, JVMState*, bool, float, ciKlass*, bool)')
f(57,16537,1,4,'InlineTree::ok_to_inline(ciMethod*, JVMState*, ciCallProfile&, bool&)')
f(58,16537,1,4,'ciMethod::get_flow_analysis()')
f(59,16537,1,4,'ciTypeFlow::do_flow()')
f(60,16537,1,4,'ciTypeFlow::flow_types()')
f(61,16537,1,4,'ciTypeFlow::df_flow_types(ciTypeFlow::Block*, bool, ciTypeFlow::StateVector*, ciTypeFlow::JsrSet*)')
f(20,16538,1,4,'PredictedCallGenerator::generate(JVMState*)')
f(21,16538,1,4,'ParseGenerator::generate(JVMState*)')
f(22,16538,1,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(23,16538,1,4,'Parse::do_all_blocks()')
f(24,16538,1,4,'Parse::do_one_block()')
f(25,16538,1,4,'Parse::do_call()')
f(26,16538,1,4,'PredictedCallGenerator::generate(JVMState*)')
f(27,16538,1,4,'ParseGenerator::generate(JVMState*)')
f(28,16538,1,4,'Parse::Parse(JVMState*, ciMethod*, float)')
f(29,16538,1,4,'Parse::do_all_blocks()')
f(30,16538,1,4,'Parse::do_one_block()')
f(31,16538,1,4,'Parse::do_if(BoolTest::mask, Node*)')
f(32,16538,1,4,'Parse::adjust_map_after_if(BoolTest::mask, Node*, float, Parse::Block*) [clone .part.0]')
f(33,16538,1,4,'GraphKit::uncommon_trap(int, ciKlass*, char const*, bool, bool)')
f(34,16538,1,4,'GraphKit::make_runtime_call(int, TypeFunc const*, unsigned char*, char const*, TypePtr const*, Node*, Node*, Node*, Node*, Node*, Node*, Node*, Node*)')
f(35,16538,1,4,'JVMState::clone_deep(Compile*) const')
f(13,16539,1,4,'Parse::do_field_access(bool, bool)')
f(14,16539,1,4,'Parse::do_get_xxx(Node*, ciField*, bool)')
f(15,16539,1,4,'GraphKit::access_load_at(Node*, Node*, TypePtr const*, Type const*, BasicType, unsigned long)')
f(16,16539,1,4,'BarrierSetC2::load_at(C2Access&, Type const*) const')
f(17,16539,1,4,'BarrierSetC2::load_at_resolved(C2Access&, Type const*) const')
f(18,16539,1,4,'GraphKit::make_load(Node*, Node*, Type const*, BasicType, int, MemNode::MemOrd, LoadNode::ControlDependency, bool, bool, bool, bool, unsigned char)')
f(19,16539,1,4,'LoadNode::make(PhaseGVN&, Node*, Node*, Node*, TypePtr const*, Type const*, BasicType, MemNode::MemOrd, LoadNode::ControlDependency, bool, bool, bool, bool, unsigned char)')
f(20,16539,1,4,'TypeNode::bottom_type() const')
f(9,16540,1,4,'PhaseRemoveUseless::PhaseRemoveUseless(PhaseGVN*, Unique_Node_List&, Phase::PhaseNumber)')
f(10,16540,1,4,'Compile::identify_useful_nodes(Unique_Node_List&)')
f(7,16541,3,4,'Compiler::compile_method(ciEnv*, ciMethod*, int, bool, DirectiveSet*)')
f(8,16541,3,4,'Compilation::Compilation(AbstractCompiler*, ciEnv*, ciMethod*, int, BufferBlob*, bool, DirectiveSet*)')
f(9,16541,3,4,'Compilation::compile_method()')
f(10,16541,3,4,'Compilation::compile_java_method()')
f(11,16541,3,4,'Compilation::emit_lir()')
f(12,16541,3,4,'LinearScan::do_linear_scan()')
f(13,16541,1,4,'LinearScan::allocate_registers()')
f(14,16541,1,4,'LinearScanWalker::activate_current()')
f(15,16541,1,4,'LinearScanWalker::alloc_free_reg(Interval*)')
f(16,16541,1,4,'LinearScanWalker::free_collect_inactive_fixed(Interval*)')
f(13,16542,2,4,'LinearScan::build_intervals()')
f(14,16542,1,4,'LinearScan::add_def(int, int, IntervalUseKind, BasicType)')
f(14,16543,1,4,'LinearScan::add_use(LIR_Opr, int, int, IntervalUseKind)')
f(15,16543,1,4,'LinearScan::create_interval(int)')
f(4,16544,25,4,'VMThread::run()')
f(5,16544,23,4,'VMThread::inner_execute(VM_Operation*)')
f(6,16544,2,4,'SafepointSynchronize::begin()')
f(7,16545,1,4,'SubTasksDone::SubTasksDone(unsigned int)')
f(6,16546,1,4,'SafepointSynchronize::end()')
f(7,16546,1,4,'SafepointSynchronize::disarm_safepoint()')
f(6,16547,20,4,'VMThread::evaluate_operation(VM_Operation*)')
f(7,16547,1,4,'PerfTraceTime::~PerfTraceTime()')
f(7,16548,19,4,'VM_Operation::evaluate()')
f(8,16548,19,4,'VM_ParallelGCFailedAllocation::doit()')
f(9,16548,19,4,'ParallelScavengeHeap::failed_mem_allocate(unsigned long)')
f(10,16548,19,4,'PSScavenge::invoke()')
f(11,16548,1,4,'GCTraceTimeTimer::at_start(TimeInstant<CompositeCounterRepresentation, CompositeElapsedCounterSource>)')
f(11,16549,1,4,'MetaspaceUtils::print_metaspace_change(MetaspaceCombinedStats const&)')
f(11,16550,17,4,'PSScavenge::invoke_no_policy() [clone .part.0]')
f(12,16550,1,4,'CollectedHeap::ensure_parsability(bool)')
f(12,16551,2,4,'GCHeapLog::log_heap(CollectedHeap*, bool) [clone .part.0]')
f(13,16551,1,4,'PSOldGen::print_on(outputStream*) const')
f(13,16552,1,4,'PSYoungGen::print_on(outputStream*) const')
f(12,16553,1,4,'OopStorage::BasicParState::~BasicParState()')
f(12,16554,1,4,'PSPromotionManager::post_scavenge(YoungGCTracer&)')
f(13,16554,1,4,'StringDedup::Requests::flush()')
f(12,16555,1,4,'PSScavenge::should_attempt_scavenge()')
f(12,16556,1,4,'ParallelScavengeHeap::ensure_parsability(bool)')
f(13,16556,1,4,'CollectedHeap::ensure_parsability(bool)')
f(14,16556,1,4,'ThreadLocalAllocStats::publish()')
f(12,16557,1,4,'ParallelScavengeHeap::update_counters()')
f(13,16557,1,4,'SpaceCounters::update_used()')
f(12,16558,2,4,'TraceMemoryManagerStats::TraceMemoryManagerStats(GCMemoryManager*, GCCause::Cause, char const*, bool, bool, bool, bool, bool, bool, bool, bool)')
f(13,16558,1,4,'GCMemoryManager::gc_begin(bool, bool, bool)')
f(13,16559,1,4,'MemoryPool::record_peak_memory_usage()')
f(12,16560,2,4,'YoungGCTracer::report_gc_end_impl(TimeInstant<CompositeCounterRepresentation, CompositeElapsedCounterSource> const&, TimePartitions*)')
f(13,16560,2,4,'GCTracer::send_phase_events(TimePartitions*) const')
f(12,16562,1,3,'[unknown]')
f(13,16562,1,4,'os::elapsed_counter()')
f(14,16562,1,3,'__clock_gettime')
f(15,16562,1,3,'[vdso]')
f(12,16563,4,4,'nmethod::oops_do_marking_epilogue()')
f(5,16567,1,4,'VMThread::wait_for_operation()')
f(5,16568,1,4,'VM_GC_Operation::cause() const')
f(4,16569,1856,4,'WorkerThread::run()')
f(5,16571,5,4,'OopStorage::BasicParState::claim_next_segment(OopStorage::BasicParState::IterationData*)')
f(5,16576,1,4,'OopStorage::BasicParState::increment_num_dead(unsigned long)')
f(5,16577,1721,4,'ScavengeRootsTask::work(unsigned int)')
f(6,16582,1,4,'ClassLoaderDataGraph::cld_do(CLDClosure*)')
f(6,16583,1,4,'OopStorage::BasicParState::claim_next_segment(OopStorage::BasicParState::IterationData*)')
f(6,16584,981,4,'PSCardTable::scavenge_contents_parallel(ObjectStartArray*, HeapWordImpl**, HeapWordImpl**, PSPromotionManager*, unsigned int, unsigned int)')
f(7,17333,109,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(8,17377,1,4,'MutableSpace::cas_allocate(unsigned long)')
f(8,17378,5,3,'__memcpy_avx512_unaligned_erms')
f(8,17383,58,4,'oopDesc* PSPromotionManager::copy_unmarked_to_survivor_space<false>(oopDesc*, markWord)')
f(9,17432,1,4,'PSOldPromotionLAB::flush()')
f(10,17432,1,3,'__tls_get_addr')
f(9,17433,6,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(9,17439,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceRefKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(10,17439,1,4,'SpanSubjectToDiscoveryClosure::do_object_b(oopDesc*)')
f(9,17440,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<ObjArrayKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(8,17441,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(7,17442,1,4,'SpinYield::yield_or_sleep()')
f(8,17442,1,3,'nanosleep')
f(7,17443,7,4,'void OopOopIterateBoundedDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_bounded<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*, MemRegion)')
f(7,17450,115,4,'void OopOopIterateBoundedDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_bounded<ObjArrayKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*, MemRegion)')
f(6,17565,19,4,'PSScavengeCLDClosure::do_cld(ClassLoaderData*)')
f(6,17584,1,4,'PSThreadRootsTaskClosure::do_thread(Thread*)')
f(6,17585,78,3,'SpinPause')
f(6,17663,7,4,'Thread::claim_par_threads_do(unsigned long)')
f(6,17670,115,4,'Threads::possibly_parallel_threads_do(bool, ThreadClosure*)')
f(7,17671,114,4,'PSThreadRootsTaskClosure::do_thread(Thread*)')
f(8,17672,6,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(9,17672,2,3,'__memcpy_avx512_unaligned_erms')
f(9,17674,4,4,'oopDesc* PSPromotionManager::copy_unmarked_to_survivor_space<false>(oopDesc*, markWord)')
f(10,17676,2,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(8,17678,1,4,'ServiceThread::oops_do_no_frames(OopClosure*, CodeBlobClosure*)')
f(8,17679,106,4,'Thread::oops_do(OopClosure*, CodeBlobClosure*)')
f(9,17679,1,4,'JavaCallWrapper::oops_do(OopClosure*)')
f(9,17680,101,4,'JavaThread::oops_do_frames(OopClosure*, CodeBlobClosure*) [clone .part.0]')
f(10,17681,1,4,'CodeCache::contains(void*)')
f(10,17682,1,4,'JNIHandleBlock::oops_do(OopClosure*)')
f(10,17683,19,4,'MarkingCodeBlobClosure::do_code_blob(CodeBlob*)')
f(11,17683,1,4,'RelocIterator::advance_over_prefix()')
f(11,17684,15,4,'nmethod::oops_do(OopClosure*, bool)')
f(12,17693,1,4,'Assembler::locate_operand(unsigned char*, Assembler::WhichOperand)')
f(12,17694,1,4,'CompiledMethod::oops_reloc_begin() const')
f(12,17695,4,4,'oop_Relocation::oop_value()')
f(13,17695,4,4,'Assembler::locate_operand(unsigned char*, Assembler::WhichOperand)')
f(11,17699,1,4,'nmethod::oops_do_try_claim()')
f(11,17700,2,4,'oop_Relocation::copy_into(RelocationHolder&) const')
f(10,17702,5,4,'RelocIterator::advance_over_prefix()')
f(10,17707,1,4,'Relocation::pd_set_data_value(unsigned char*, long, bool)')
f(10,17708,3,4,'StackFrameStream::StackFrameStream(JavaThread*, bool, bool, bool)')
f(11,17708,2,4,'CodeCache::find_blob(void*)')
f(11,17710,1,4,'CodeHeap::find_blob(void*) const')
f(10,17711,19,4,'frame::oops_code_blob_do(OopClosure*, CodeBlobClosure*, DerivedOopClosure*, DerivedPointerIterationMode, RegisterMap const*) const')
f(11,17712,18,4,'ImmutableOopMap::oops_do(frame const*, RegisterMap const*, OopClosure*, DerivedPointerIterationMode) const')
f(12,17712,1,4,'AddDerivedOop::do_derived_oop(derived_base*, derived_pointer*)')
f(12,17713,17,4,'void OopMapDo<OopClosure, DerivedOopClosure, SkipNullValue>::iterate_oops_do<RegisterMap>(frame const*, RegisterMap const*, ImmutableOopMap const*)')
f(13,17722,2,4,'OopMapStream::find_next() [clone .part.0]')
f(13,17724,1,4,'PSRootsClosure<false>::do_oop(narrowOop*)')
f(14,17724,1,4,'oopDesc* PSPromotionManager::copy_unmarked_to_survivor_space<false>(oopDesc*, markWord)')
f(13,17725,5,4,'PSRootsClosure<false>::do_oop(oopDesc**)')
f(14,17728,1,4,'oopDesc* PSPromotionManager::copy_unmarked_to_survivor_space<false>(oopDesc*, markWord)')
f(15,17728,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(14,17729,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(10,17730,1,4,'frame::oops_do_internal(OopClosure*, CodeBlobClosure*, DerivedOopClosure*, DerivedPointerIterationMode, RegisterMap const*, bool) const')
f(10,17731,22,4,'frame::oops_interpreted_do(OopClosure*, RegisterMap const*, bool) const')
f(11,17736,10,4,'ConstantPool::name_and_type_ref_index_at(int, Bytecodes::Code)')
f(11,17746,1,4,'ConstantPool::signature_ref_index_at(int)')
f(11,17747,6,4,'OopMapCache::lookup(methodHandle const&, int, InterpreterOopMap*)')
f(12,17752,1,4,'InterpreterOopMap::copy_from(OopMapCacheEntry const*)')
f(10,17753,13,4,'frame::sender(RegisterMap*) const')
f(11,17762,1,4,'CodeCache::find_blob(void*)')
f(11,17763,1,4,'Continuation::is_return_barrier_entry(unsigned char*)')
f(11,17764,1,4,'frame::sender_for_interpreter_frame(RegisterMap*) const')
f(11,17765,1,3,'update_register_map1(ImmutableOopMap const*, frame const*, RegisterMap*)')
f(12,17765,1,4,'OopMapStream::find_next() [clone .part.0]')
f(10,17766,1,4,'metadata_Relocation::fix_metadata_relocation()')
f(10,17767,1,4,'methodHandle::~methodHandle()')
f(10,17768,11,4,'nmethod::fix_oop_relocations(unsigned char*, unsigned char*, bool)')
f(11,17775,1,4,'RelocIterator::initialize(CompiledMethod*, unsigned char*, unsigned char*)')
f(11,17776,1,4,'Relocation::pd_set_data_value(unsigned char*, long, bool)')
f(12,17776,1,4,'Assembler::locate_operand(unsigned char*, Assembler::WhichOperand)')
f(11,17777,1,4,'metadata_Relocation::fix_metadata_relocation()')
f(11,17778,1,4,'oop_Relocation::offset()')
f(10,17779,1,4,'nmethod::oops_do_try_claim()')
f(10,17780,1,4,'oop_Relocation::copy_into(RelocationHolder&) const')
f(9,17781,1,4,'ServiceThread::oops_do_no_frames(OopClosure*, CodeBlobClosure*)')
f(10,17781,1,4,'JavaThread::oops_do_no_frames(OopClosure*, CodeBlobClosure*)')
f(11,17781,1,4,'HandleArea::oops_do(OopClosure*)')
f(9,17782,1,4,'Thread::oops_do_no_frames(OopClosure*, CodeBlobClosure*)')
f(9,17783,1,4,'frame::oops_code_blob_do(OopClosure*, CodeBlobClosure*, DerivedOopClosure*, DerivedPointerIterationMode, RegisterMap const*) const')
f(9,17784,1,4,'frame::oops_do_internal(OopClosure*, CodeBlobClosure*, DerivedOopClosure*, DerivedPointerIterationMode, RegisterMap const*, bool) const')
f(6,17785,6,3,'__GI_sched_yield')
f(6,17791,68,3,'__memcpy_avx512_unaligned_erms')
f(6,17859,3,3,'__memcpy_chk@plt')
f(6,17862,1,3,'__memset_avx512_unaligned_erms')
f(6,17863,1,3,'memset@plt')
f(6,17864,431,3,'steal_work(TaskTerminator&, unsigned int)')
f(7,17935,327,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(8,18038,1,4,'MutableSpace::cas_allocate(unsigned long)')
f(8,18039,1,4,'PSPromotionManager::process_array_chunk(PartialArrayScanTask)')
f(8,18040,5,3,'__memcpy_avx512_unaligned_erms')
f(8,18045,1,3,'memcpy@plt')
f(8,18046,214,4,'oopDesc* PSPromotionManager::copy_unmarked_to_survivor_space<false>(oopDesc*, markWord)')
f(9,18234,1,4,'PSOldGen::expand_for_allocate(unsigned long)')
f(10,18234,1,3,'pthread_mutex_lock')
f(9,18235,3,4,'PSOldPromotionLAB::flush()')
f(10,18235,1,4,'CollectedHeap::fill_with_object(HeapWordImpl**, unsigned long, bool)')
f(10,18236,2,3,'__tls_get_addr')
f(9,18238,20,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(9,18258,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceRefKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(10,18258,1,4,'PSIsAliveClosure::do_object_b(oopDesc*)')
f(9,18259,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<ObjArrayKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(8,18260,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(8,18261,1,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<ObjArrayKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(7,18262,3,4,'PSPromotionManager::process_array_chunk(PartialArrayScanTask)')
f(7,18265,3,3,'SpinPause')
f(7,18268,5,4,'TaskTerminator::offer_termination(TerminatorTerminator*)')
f(8,18270,1,4,'Monitor::wait_without_safepoint_check(unsigned long)')
f(8,18271,2,3,'pthread_mutex_lock')
f(7,18273,2,3,'__memcpy_avx512_unaligned_erms')
f(7,18275,1,3,'__pthread_cond_broadcast')
f(7,18276,19,4,'oopDesc* PSPromotionManager::copy_unmarked_to_survivor_space<false>(oopDesc*, markWord)')
f(8,18291,4,4,'void OopOopIterateBackwardsDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_backwards<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*)')
f(6,18295,3,4,'void OopOopIterateBoundedDispatch<PSPushContentsClosure>::Table::oop_oop_iterate_bounded<InstanceKlass, narrowOop>(PSPushContentsClosure*, oopDesc*, Klass*, MemRegion)')
f(5,18298,127,4,'void WeakProcessor::Task::work<PSIsAliveClosure, PSAdjustWeakRootsClosure>(unsigned int, PSIsAliveClosure*, PSAdjustWeakRootsClosure*)')
f(6,18415,1,4,'OopStorage::BasicParState::claim_next_segment(OopStorage::BasicParState::IterationData*)')
f(6,18416,1,4,'WeakProcessorParTimeTracker::WeakProcessorParTimeTracker(WeakProcessorTimes*, OopStorageSet::WeakId, unsigned int)')
f(6,18417,3,4,'WeakProcessorParTimeTracker::~WeakProcessorParTimeTracker()')
f(7,18419,1,4,'CompositeElapsedCounterSource::seconds(PairRep<long, long>)')
f(6,18420,5,4,'WeakProcessorTimes::record_worker_items(unsigned int, OopStorageSet::WeakId, unsigned long, unsigned long)')
f(4,18425,1,3,'__tls_get_addr')
f(4,18426,2,4,'void WeakProcessor::WeakOopsDoTask::erased_do_work<PSIsAliveClosure, PSAdjustWeakRootsClosure>(WeakProcessor::WeakOopsDoTask*, unsigned int)')
f(1,18428,1,3,'unlink_chunk.isra.2')

search();
</script></body></html>
