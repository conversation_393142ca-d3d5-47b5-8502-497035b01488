package io.hydrax.pricestreaming.cache;

import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.common.TimeInForceEnum;
import io.hydrax.pricestreaming.domain.TradingVenueDTO;
import io.hydrax.proto.metwo.match.TickerRoute;
import io.hydrax.proto.metwo.match.TimeInForce;
import io.hydrax.proto.metwo.match.VenueMarketUpdateRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TradingVenueCache {
  Map<String, TradingVenueDTO> tradingVenues = new ConcurrentHashMap<>();
  Map<String, List<String>> symbolCodes = new ConcurrentHashMap<>();

  // Performance optimization caches
  // Cache for venue codes by timeInForce + orderType + ticker combination
  private final Map<String, List<String>> venueCodesByQuery = new ConcurrentHashMap<>();
  // Cache for venue ticker codes to avoid repeated Set creation
  private final Map<String, Set<String>> venueTickerCodes = new ConcurrentHashMap<>();

  // Advanced optimization: Index structures for faster lookups
  // Index: ticker -> set of venue codes that support this ticker
  private final Map<String, Set<String>> tickerToVenuesIndex = new ConcurrentHashMap<>();
  // Index: orderType -> set of venue codes that support this order type
  private final Map<String, Set<String>> orderTypeToVenuesIndex = new ConcurrentHashMap<>();
  // Index: timeInForce + orderType -> set of venue codes
  private final Map<String, Set<String>> timeInForceOrderTypeToVenuesIndex =
      new ConcurrentHashMap<>();

  // Cache statistics for monitoring
  private volatile long cacheHits = 0;
  private volatile long cacheMisses = 0;

  public TradingVenueDTO get(String marketCode, String venueMarketCode) {
    return tradingVenues.get(joinKey(marketCode, venueMarketCode));
  }

  public void put(VenueMarketUpdateRequest request) {
    // Generate a TradingVenue instance based on the request
    TradingVenueDTO venue = TradingVenueDTO.from(request);
    this.put(request.getMarketCode(), request.getCode(), venue);
  }

  public void put(String marketCode, String venueMarketCode, TradingVenueDTO venue) {
    // Create composite key for this venue
    String compositeKey = joinKey(marketCode, venueMarketCode);

    // Update the tradingVenues map with the new or updated TradingVenue data
    tradingVenues.put(compositeKey, venue);

    // Clear performance caches for this venue using composite key
    clearCachesForVenue(compositeKey);

    // Update the symbolCodes cache
    // Remove any existing cache entries related to the current venue
    symbolCodes.entrySet().removeIf(entry -> entry.getKey().startsWith(venueMarketCode + "_"));

    // Add new symbolCodes data to the cache
    venue
        .getTickersRoute()
        .forEach(
            tickerRoute -> {
              String lpTickerName = tickerRoute.getLpTickerName();
              String cacheKey = venueMarketCode + "_" + lpTickerName;

              // Update the cache with the new ticker codes for the given lpTickerName
              symbolCodes.computeIfAbsent(
                  cacheKey,
                  key ->
                      venue.getTickersRoute().stream()
                          .filter(
                              ticker ->
                                  lpTickerName.equals(
                                      ticker.getLpTickerName())) // Filter by lpTickerName
                          .map(TickerRoute::getTickerCode) // Extract tickerCode
                          .toList() // Collect as an immutable list
                  );
            });

    // Pre-compute and cache venue ticker codes for performance
    cacheVenueTickerCodes(venueMarketCode, venue);

    // Update indexes for faster lookups using composite key
    updateIndexes(compositeKey, venue);
  }

  public List<TradingVenueDTO> getAll() {
    return List.copyOf(tradingVenues.values());
  }

  public void remove(VenueMarketUpdateRequest request) {
    String compositeKey = joinKey(request.getMarketCode(), request.getCode());
    tradingVenues.remove(compositeKey);
    // Clear performance caches for this venue using composite key
    clearCachesForVenue(compositeKey);
  }

  public List<String> selectCodeByTimeInForceAndOrderType(
      String timeInForce, String orderType, String ticker) {
    // Create cache key for this query - optimized string concatenation
    String queryKey = timeInForce + "|" + orderType + "|" + ticker;

    // Check cache first - fast path for cache hits
    List<String> cachedResult = venueCodesByQuery.get(queryKey);
    if (cachedResult != null) {
      cacheHits++;
      return cachedResult;
    }

    // Cache miss - compute result
    cacheMisses++;

    // Convert types once (these methods already use static caches)
    io.hydrax.proto.metwo.match.PsOrderType psOrderType = OrderType.from(orderType);
    TimeInForce timeInForceProto = TimeInForceEnum.from(timeInForce);

    if (psOrderType == null || timeInForceProto == null) {
      if (log.isWarnEnabled()) {
        log.warn("Invalid orderType: {} or timeInForce: {}", orderType, timeInForce);
      }
      return Collections.emptyList();
    }

    // Use index-based optimization for better performance
    List<String> result =
        computeVenueCodesWithIndexes(psOrderType, timeInForceProto, orderType, ticker);

    // Cache the result
    venueCodesByQuery.put(queryKey, result);

    return result;
  }

  String joinKey(String marketCode, String venueMarketCode) {
    return marketCode + ":" + venueMarketCode;
  }

  public String getLpTickerName(String marketCode, String venueMarketCode, String symbol) {
    return this.get(marketCode, venueMarketCode).getTickersRoute().stream()
        .filter(tickerRoute -> tickerRoute.getTickerCode().equals(symbol))
        .findFirst()
        .orElse(TickerRoute.newBuilder().build())
        .getLpTickerName();
  }

  public List<String> getMarketCodeByVenueCode(String venueCode) {
    return tradingVenues.values().stream()
        .filter(venue -> venue.getCode().equals(venueCode)) // Filter by code
        .map(TradingVenueDTO::getMarketCode) // Get market code
        .toList();
  }

  public String getLpTickerName(String marketCode, String symbol) {
    return tradingVenues.values().stream()
        .filter(venue -> venue.getMarketCode().equals(marketCode))
        .flatMap(v -> v.getTickersRoute().stream())
        .filter(tickerRoute -> tickerRoute.getTickerCode().equals(symbol))
        .findFirst()
        .orElse(TickerRoute.newBuilder().build())
        .getLpTickerName();
  }

  public List<String> getSymbolCodesByVenueMarketAndVenueSymbol(
      String venueMarketCode, String lpTickerName) {
    // Generate the cache key by combining marketCode and lpTickerName.
    String cacheKey = venueMarketCode + "_" + lpTickerName;

    // Retrieve the ticker codes from the cache if available; otherwise, compute and cache the
    // result.
    return symbolCodes.computeIfAbsent(
        cacheKey,
        key ->
            tradingVenues.values().stream()
                .filter(venue -> venueMarketCode.equals(venue.getCode())) // Filter by marketCode.
                .flatMap(
                    venue -> venue.getTickersRoute().stream()) // Flatten the list of ticker routes.
                .filter(
                    ticker ->
                        lpTickerName.equals(ticker.getLpTickerName())) // Filter by lpTickerName.
                .map(TickerRoute::getTickerCode) // Extract the tickerCode.
                .toList() // Collect as an immutable list.
        );
  }

  // Performance optimization helper methods

  /**
   * Optimized venue matching with early exits and cached ticker codes
   */
  private boolean isVenueMatchingOptimized(
      TradingVenueDTO venue,
      io.hydrax.proto.metwo.match.PsOrderType psOrderType,
      TimeInForce timeInForceProto,
      String orderType,
      String ticker) {
    // Early exit: Check ticker first (most selective filter)
    // For ticker codes, we use venue.getCode() since tickers are unique per venue regardless of
    // market
    Set<String> tickerCodes = getCachedVenueTickerCodes(venue.getCode(), venue);
    if (!tickerCodes.contains(ticker)) {
      return false;
    }

    // Check if the venue contains the order type
    if (!venue.getOrderTypes().contains(psOrderType)) {
      return false;
    }

    // Check if the venue contains the time in force for this order type
    Set<TimeInForce> timeInForceSet = venue.getTimeInForces().get(orderType);
    return timeInForceSet != null && timeInForceSet.contains(timeInForceProto);
  }

  /**
   * Gets cached venue ticker codes, computing if not present
   */
  private Set<String> getCachedVenueTickerCodes(String compositeKey, TradingVenueDTO venue) {
    // Extract venueMarketCode from composite key for ticker cache
    String venueMarketCode = extractVenueMarketCodeFromCompositeKey(compositeKey);
    if (venueMarketCode == null) {
      // Fallback: if it's not a composite key, use it directly
      venueMarketCode = compositeKey;
    }

    return venueTickerCodes.computeIfAbsent(
        venueMarketCode,
        key -> {
          return Optional.ofNullable(venue.getTickersRoute())
              .orElse(Collections.emptyList())
              .stream()
              .map(TickerRoute::getTickerCode)
              .collect(Collectors.toSet());
        });
  }

  /**
   * Pre-computes and caches venue ticker codes for performance
   */
  private void cacheVenueTickerCodes(String venueMarketCode, TradingVenueDTO venue) {
    // For backward compatibility, we still use venueMarketCode for ticker caching
    // since ticker codes are unique per venue regardless of market
    Set<String> tickerCodes =
        Optional.ofNullable(venue.getTickersRoute()).orElse(Collections.emptyList()).stream()
            .map(TickerRoute::getTickerCode)
            .collect(Collectors.toSet());
    venueTickerCodes.put(venueMarketCode, tickerCodes);
  }

  /**
   * Computes venue codes using index-based optimization with direct venue lookup
   */
  private List<String> computeVenueCodesWithIndexes(
      io.hydrax.proto.metwo.match.PsOrderType psOrderType,
      TimeInForce timeInForceProto,
      String orderType,
      String ticker) {
    // Use the most specific index first - time in force + order type combination
    String tifOrderTypeKey = orderType + "|" + timeInForceProto.name();
    Set<String> tifOrderTypeVenues = timeInForceOrderTypeToVenuesIndex.get(tifOrderTypeKey);

    if (tifOrderTypeVenues == null || tifOrderTypeVenues.isEmpty()) {
      return Collections.emptyList();
    }

    // Get ticker venues
    Set<String> tickerVenues = tickerToVenuesIndex.get(ticker);
    if (tickerVenues == null || tickerVenues.isEmpty()) {
      return Collections.emptyList();
    }

    // Find intersection without creating intermediate sets - iterate over smaller set
    Set<String> smallerSet =
        tifOrderTypeVenues.size() <= tickerVenues.size() ? tifOrderTypeVenues : tickerVenues;
    Set<String> largerSet =
        tifOrderTypeVenues.size() > tickerVenues.size() ? tifOrderTypeVenues : tickerVenues;

    List<String> result = new ArrayList<>();
    for (String compositeKey : smallerSet) {
      if (largerSet.contains(compositeKey)) {
        // Extract venueMarketCode from composite key (marketCode:venueMarketCode)
        String venueMarketCode = extractVenueMarketCodeFromCompositeKey(compositeKey);
        if (venueMarketCode != null) {
          result.add(venueMarketCode);
        }
      }
    }

    return result;
  }

  /**
   * Extracts venueMarketCode from composite key (marketCode:venueMarketCode)
   */
  private String extractVenueMarketCodeFromCompositeKey(String compositeKey) {
    int colonIndex = compositeKey.indexOf(':');
    if (colonIndex > 0 && colonIndex < compositeKey.length() - 1) {
      return compositeKey.substring(colonIndex + 1);
    }
    return null;
  }

  /**
   * Updates all indexes when a venue is added or modified
   * First removes old index entries, then adds new ones
   */
  private void updateIndexes(String compositeKey, TradingVenueDTO venue) {
    // First, remove any existing index entries for this venue
    clearVenueFromIndexes(compositeKey);

    // Then add new index entries
    addVenueToIndexes(compositeKey, venue);
  }

  /**
   * Adds a venue to all relevant indexes
   */
  private void addVenueToIndexes(String compositeKey, TradingVenueDTO venue) {
    // Update ticker index
    Set<String> tickerCodes = getCachedVenueTickerCodes(compositeKey, venue);
    for (String ticker : tickerCodes) {
      tickerToVenuesIndex
          .computeIfAbsent(ticker, k -> ConcurrentHashMap.newKeySet())
          .add(compositeKey);
    }

    // Update order type index
    for (var orderType : venue.getOrderTypes()) {
      String orderTypeName = getOrderTypeName(orderType);
      if (orderTypeName != null) {
        orderTypeToVenuesIndex
            .computeIfAbsent(orderTypeName, k -> ConcurrentHashMap.newKeySet())
            .add(compositeKey);
      }
    }

    // Update time in force + order type index
    for (Map.Entry<String, Set<TimeInForce>> entry : venue.getTimeInForces().entrySet()) {
      String orderType = entry.getKey();
      for (TimeInForce tif : entry.getValue()) {
        String key = orderType + "|" + tif.name();
        timeInForceOrderTypeToVenuesIndex
            .computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet())
            .add(compositeKey);
      }
    }
  }

  /**
   * Helper method to get order type name from PsOrderType
   */
  private String getOrderTypeName(io.hydrax.proto.metwo.match.PsOrderType orderType) {
    return switch (orderType) {
      case PS_ORDER_TYPE_MARKET -> "market";
      case PS_ORDER_TYPE_LIMIT -> "limit";
      case PS_ORDER_TYPE_STOP -> "stop";
      default -> null;
    };
  }

  /**
   * Clears all performance caches related to a specific venue
   */
  private void clearCachesForVenue(String compositeKey) {
    // Extract venueMarketCode from composite key for ticker cache cleanup
    String venueMarketCode = extractVenueMarketCodeFromCompositeKey(compositeKey);
    if (venueMarketCode != null) {
      // Clear venue-specific caches (ticker cache uses venueMarketCode)
      venueTickerCodes.remove(venueMarketCode);
    }

    // Clear from indexes (indexes use composite key)
    clearVenueFromIndexes(compositeKey);

    // Clear query caches that might be affected by this venue
    venueCodesByQuery.clear(); // Simple approach - clear all query cache
  }

  /**
   * Removes a venue from all indexes and cleans up empty entries
   */
  private void clearVenueFromIndexes(String compositeKey) {
    // Remove from ticker index
    tickerToVenuesIndex.values().forEach(venues -> venues.remove(compositeKey));

    // Remove from order type index
    orderTypeToVenuesIndex.values().forEach(venues -> venues.remove(compositeKey));

    // Remove from time in force + order type index
    timeInForceOrderTypeToVenuesIndex.values().forEach(venues -> venues.remove(compositeKey));

    // Clean up empty sets to prevent memory leaks
    cleanupEmptyIndexEntries();
  }

  /**
   * Removes empty sets from indexes to prevent memory leaks
   */
  private void cleanupEmptyIndexEntries() {
    // Clean ticker index
    tickerToVenuesIndex.entrySet().removeIf(entry -> entry.getValue().isEmpty());

    // Clean order type index
    orderTypeToVenuesIndex.entrySet().removeIf(entry -> entry.getValue().isEmpty());

    // Clean time in force + order type index
    timeInForceOrderTypeToVenuesIndex.entrySet().removeIf(entry -> entry.getValue().isEmpty());
  }

  /**
   * Get cache statistics for monitoring
   */
  public String getCacheStats() {
    long total = cacheHits + cacheMisses;
    double hitRate = total > 0 ? (double) cacheHits / total * 100 : 0;
    return String.format(
        "Cache Stats - Hits: %d, Misses: %d, Hit Rate: %.2f%%, "
            + "Query Cache Size: %d, Venue Ticker Cache Size: %d",
        cacheHits, cacheMisses, hitRate, venueCodesByQuery.size(), venueTickerCodes.size());
  }

  /**
   * Rebuilds the indexes from existing trading venues
   * This method can be called to ensure consistency if needed
   */
  public void rebuildIndexes() {
    // Clear all indexes
    tickerToVenuesIndex.clear();
    orderTypeToVenuesIndex.clear();
    timeInForceOrderTypeToVenuesIndex.clear();

    // Rebuild indexes from existing venues
    for (Map.Entry<String, TradingVenueDTO> entry : tradingVenues.entrySet()) {
      String compositeKey = entry.getKey(); // This is already marketCode:venueMarketCode
      TradingVenueDTO venue = entry.getValue();
      addVenueToIndexes(compositeKey, venue);
    }
    log.info("Rebuilt indexes with {} venues", tradingVenues.size());
  }

  /**
   * Precompute common queries for better performance
   */
  public void precomputeCommonQueries() {
    if (tradingVenues.isEmpty()) {
      return;
    }

    // Common combinations to precompute
    String[] commonOrderTypes = {"limit", "market"};
    String[] commonTimeInForces = {"gtc", "ioc", "fok"};
    Set<String> commonTickers = tickerToVenuesIndex.keySet();

    log.info(
        "Precomputing {} common query combinations",
        commonOrderTypes.length * commonTimeInForces.length * commonTickers.size());

    for (String orderType : commonOrderTypes) {
      for (String timeInForce : commonTimeInForces) {
        for (String ticker : commonTickers) {
          // This will populate the cache
          selectCodeByTimeInForceAndOrderType(timeInForce, orderType, ticker);
        }
      }
    }

    log.info("Precomputation completed. {}", getCacheStats());
  }
}
