package io.hydrax.pricestreaming.router;

import io.hydrax.pricestreaming.cache.OrderRoutingStrategyCache;
import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.domain.OrderRoutingStrategyDTO;
import io.hydrax.proto.metwo.match.*;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * JMH Benchmark for Route and Match methods performance testing
 * 
 * Run with: ./gradlew jmh
 * Or: java -jar build/libs/benchmarks.jar
 */
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(value = 2, jvmArgs = {"-Xms2G", "-Xmx2G"})
@Warmup(iterations = 3, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 1, timeUnit = TimeUnit.SECONDS)
public class RoutingBenchmark {

    private RoutingEngine routingEngine;
    private OrderRoutingStrategyCache cache;
    
    // Test data
    private Order limitOrder;
    private Order marketOrder;
    private Order stopOrder;
    private Order unmatchedOrder;
    
    private List<String> venueCodesSmall;
    private List<String> venueCodesLarge;
    
    private OrderRoutingStrategyDTO strategy1;
    private OrderRoutingStrategyDTO strategy2;
    private OrderRoutingStrategyDTO strategy3;

    @Setup(Level.Trial)
    public void setupBenchmark() {
        cache = new OrderRoutingStrategyCache();
        routingEngine = new RoutingEngine(() -> cache.getAll());
        
        // Create test strategies
        setupTestStrategies();
        
        // Create test orders
        setupTestOrders();
        
        // Create venue codes
        venueCodesSmall = Arrays.asList("VENUE1", "VENUE2");
        venueCodesLarge = Arrays.asList("VENUE1", "VENUE2", "VENUE3", "VENUE4", "VENUE5", 
                                       "VENUE6", "VENUE7", "VENUE8", "VENUE9", "VENUE10");
    }

    private void setupTestStrategies() {
        // Strategy 1: Limit orders, multiple venues
        strategy1 = OrderRoutingStrategyDTO.builder()
                .code("strategy-limit")
                .type("sequence")
                .orderTypes(Set.of(PsOrderType.PS_ORDER_TYPE_LIMIT))
                .timeInForces(Map.of(
                    OrderType.LIMIT.getName(), 
                    Set.of(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL, 
                           TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL)
                ))
                .tradingVenues(Arrays.asList("VENUE1", "VENUE2", "VENUE3"))
                .tickers(Arrays.asList("BTC/USD", "ETH/USD", "LTC/USD"))
                .build();
        
        // Strategy 2: Market orders, fewer venues
        strategy2 = OrderRoutingStrategyDTO.builder()
                .code("strategy-market")
                .type("best-price")
                .orderTypes(Set.of(PsOrderType.PS_ORDER_TYPE_MARKET))
                .timeInForces(Map.of(
                    OrderType.MARKET.getName(), 
                    Set.of(TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL, 
                           TimeInForce.TIME_IN_FORCE_FILL_OR_KILL)
                ))
                .tradingVenues(Arrays.asList("VENUE1", "VENUE2"))
                .tickers(Arrays.asList("BTC/USD", "ETH/USD"))
                .build();
        
        // Strategy 3: Stop orders, single venue
        strategy3 = OrderRoutingStrategyDTO.builder()
                .code("strategy-stop")
                .type("sequence")
                .orderTypes(Set.of(PsOrderType.PS_ORDER_TYPE_STOP))
                .timeInForces(Map.of(
                    OrderType.STOP.getName(), 
                    Set.of(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
                ))
                .tradingVenues(Arrays.asList("VENUE1"))
                .tickers(Arrays.asList("BTC/USD"))
                .build();
        
        cache.put("strategy-limit", strategy1);
        cache.put("strategy-market", strategy2);
        cache.put("strategy-stop", strategy3);
    }

    private void setupTestOrders() {
        // Limit order that matches strategy1
        limitOrder = Order.builder()
                .psOrder(PsOrder.newBuilder()
                    .setSymbol("BTC/USD")
                    .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
                    .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
                    .setSide(Side.SIDE_BUY)
                    .setQty(UDec128.newBuilder().setLow(1000000000000000000L).build()) // 1.0
                    .setPrice(UDec128.newBuilder().setHigh(2L).setLow(7766279631452241920L).build()) // 50000.0
                    .build())
                .build();
        
        // Market order that matches strategy2
        marketOrder = Order.builder()
                .psOrder(PsOrder.newBuilder()
                    .setSymbol("ETH/USD")
                    .setOrdType(PsOrderType.PS_ORDER_TYPE_MARKET)
                    .setTimeInForce(TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL)
                    .setSide(Side.SIDE_SELL)
                    .setQty(UDec128.newBuilder().setLow(5000000000000000000L).build()) // 5.0
                    .build())
                .build();
        
        // Stop order that matches strategy3
        stopOrder = Order.builder()
                .psOrder(PsOrder.newBuilder()
                    .setSymbol("BTC/USD")
                    .setOrdType(PsOrderType.PS_ORDER_TYPE_STOP)
                    .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
                    .setSide(Side.SIDE_BUY)
                    .setQty(UDec128.newBuilder().setLow(2000000000000000000L).build()) // 2.0
                    .setPrice(UDec128.newBuilder().setHigh(2L).setLow(7766279631452241920L).build()) // 50000.0
                    .build())
                .build();
        
        // Order that doesn't match any strategy
        unmatchedOrder = Order.builder()
                .psOrder(PsOrder.newBuilder()
                    .setSymbol("DOGE/USD") // Different ticker
                    .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
                    .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
                    .setSide(Side.SIDE_BUY)
                    .setQty(UDec128.newBuilder().setLow(1000000000000000000L).build())
                    .setPrice(UDec128.newBuilder().setLow(1000000000000000000L).build())
                    .build())
                .build();
    }

    // ========== Route Method Benchmarks ==========
    
    @Benchmark
    public void routeLimitOrderSmallVenues(Blackhole bh) {
        try {
            routingEngine.route(limitOrder, venueCodesSmall);
        } catch (Exception e) {
            // Expected for some test cases
        }
    }
    
    @Benchmark
    public void routeMarketOrderSmallVenues(Blackhole bh) {
        try {
            routingEngine.route(marketOrder, venueCodesSmall);
        } catch (Exception e) {
            // Expected for some test cases
        }
    }
    
    @Benchmark
    public void routeLimitOrderLargeVenues(Blackhole bh) {
        try {
            routingEngine.route(limitOrder, venueCodesLarge);
        } catch (Exception e) {
            // Expected for some test cases
        }
    }
    
    @Benchmark
    public void routeUnmatchedOrder(Blackhole bh) {
        try {
            routingEngine.route(unmatchedOrder, venueCodesSmall);
        } catch (Exception e) {
            // Expected - this should throw RejectionException
        }
    }

    // ========== Match Method Benchmarks ==========
    
    @Benchmark
    public boolean matchLimitOrderStrategy1() {
        return strategy1.match(limitOrder);
    }
    
    @Benchmark
    public boolean matchMarketOrderStrategy2() {
        return strategy2.match(marketOrder);
    }
    
    @Benchmark
    public boolean matchStopOrderStrategy3() {
        return strategy3.match(stopOrder);
    }
    
    @Benchmark
    public boolean matchUnmatchedOrderStrategy1() {
        return strategy1.match(unmatchedOrder);
    }
    
    @Benchmark
    public boolean matchWrongOrderTypeStrategy() {
        // Test market order against limit strategy (should fail fast)
        return strategy1.match(marketOrder);
    }
    
    @Benchmark
    public boolean matchWrongTickerStrategy() {
        // Test unmatched order (wrong ticker, should fail fast)
        return strategy1.match(unmatchedOrder);
    }

    // ========== Combined Benchmarks ==========
    
    @Benchmark
    public void routeMultipleOrderTypes(Blackhole bh) {
        try {
            routingEngine.route(limitOrder, venueCodesSmall);
            routingEngine.route(marketOrder, venueCodesSmall);
            routingEngine.route(stopOrder, venueCodesSmall);
        } catch (Exception e) {
            // Expected for some test cases
        }
    }
    
    @Benchmark
    public int matchAllStrategiesAgainstOrder() {
        int matches = 0;
        if (strategy1.match(limitOrder)) matches++;
        if (strategy2.match(limitOrder)) matches++;
        if (strategy3.match(limitOrder)) matches++;
        return matches;
    }
}
